package com.westlakefinancial.technology.partner.batch.mongo;

import com.westlakefinancial.technology.partner.batch.config.Config;
import com.westlakefinancial.technology.partner.batch.domain.MailTriggeredFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * The type Mail triggered file mongo repo.
 *
 * <AUTHOR>
@Slf4j
@Repository
public class MailTriggeredFileMongoRepoImpl implements MailTriggeredFileMongoRepo {

    /**
     * The Mongo template.
     */
    private final MongoTemplate mongoTemplate;

    /**
     * The Config.
     */
    private final Config config;

    /**
     * Instantiates a new Mail triggered file mongo repo.
     *
     * @param mongoTemplate the mongo template
     * @param config        the config
     */
    public MailTriggeredFileMongoRepoImpl(MongoTemplate mongoTemplate, Config config) {
        this.mongoTemplate = mongoTemplate;
        this.config = config;
    }

    @Override
    public List<MailTriggeredFile> getAllFiles() {
        return mongoTemplate.findAll(MailTriggeredFile.class, config.getMongoDBCollection());
    }

    @Override
    public List<MailTriggeredFile> getUnresolvedFiles() {
        return mongoTemplate.find(Query.query(Criteria.where("isResolved").ne("Y")), MailTriggeredFile.class, config.getMongoDBCollection());
    }

    @Override
    public void updateFile(MailTriggeredFile file) {
        mongoTemplate.save(file, config.getMongoDBCollection());
    }

}

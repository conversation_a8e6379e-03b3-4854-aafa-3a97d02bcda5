package com.westlakefinancial.technology.partner.batch.domain;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Locale;

/**
 * Created by <PERSON><PERSON><PERSON> on 7/10/2014.
 */
public class DcConstant {

    public static DateFormat MMddyyyySlashDateFormat = new SimpleDateFormat("MM/dd/yyyy");

    public static final String DEALER_CODE = "[DEALER_CODE]";

    public static final String ACC_AAD_ID = "accAadId";
    public static final String CURRENT_DATE = "[CURRENT_DATE]";

    public static final String DEALER_NAME = "[DEALER_NAME]";

    public static final String CONTRACT_DATE = "[CONTRACT_DATE]";

    public static final String CUSTOMER_NAME = "[CUSTOMER_NAME]";

    public static final String ASSET_VIN = "[ASSET_VIN]";

    public static final String ASSET_NAME = "[ASSET_NAME]";

    public static final String ACCOUNT_NUMBER = "[ACC_NBR]";

    public static final String TOTAL_AMOUNT = "[TOTAL_AMOUNT]";

    public static final String NO_LATE_DATE = "[NO_LATE_DATE]";

    public static DateFormat yyyyMMddHHmmssDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

    public static String TEMPLATE_FILE_NAME = "templateFileName";

    public static String EMAIL_FROM = "<EMAIL>";

    public static final String UNPAID_PRINCIPAL = "Unpaid Principal";
    public static final String UNEARNED_INSURANCE = "Unearned Insurance";

    public static final String TOTAL_DUE = "Total Due";
    public static final String EARNED_INTEREST = "Earned Interest";
    public static final String RECOVERY_FEES = "Recovery Fees";
    public static final String SERVICING_OTHER_FEES = "Servicing and Other Fees";

    public static final String ACC_NBR = "accNbr";

    public static final String FILE_NAME = "Westlake_Direct_Template_TITLE.docx";

    public static final String FULL_NAME = "fullname";

    public static final String EMAIL = "email";

    public static final String ROLE_NAME = "roleName";

    public static final String TITLE_AUTO_BATCH = "TITLE_AUTO_BATCH";

    public static final String DC_AGENT = "DcAgent";

    public static final String TITLE_AUTO_BATCH_FLAG = "Y";


    public static String formatDouble(double d) {
        return convertCurrency(String.format("%.2f", new BigDecimal(d)));
    }


    /**
     * @param amount
     * @return String: convert string amount to currency
     */
    public static String convertCurrency(String amount) {
        DecimalFormat formatter = (DecimalFormat) NumberFormat.getCurrencyInstance(Locale.ENGLISH);
        String symbol = "$";
        formatter.setPositivePrefix(symbol);
        formatter.setNegativePrefix("-" + symbol);

        return formatter.format(new BigDecimal(amount));
    }


}

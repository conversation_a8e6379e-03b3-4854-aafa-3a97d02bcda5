package com.westlakefinancial.technology.partner.batch.writer;

import com.westlakefinancial.technology.partner.batch.domain.AutoCheckMileage;
import com.westlakefinancial.technology.partner.batch.mapper.war.WarDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/2/26 10:26
 */
@Component
@Slf4j
public class AutoCheckMileageWriter implements ItemWriter<AutoCheckMileage> {

    @Resource
    private WarDao warDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void write(List<? extends AutoCheckMileage> autoCheckMileagesList) {

        log.info("AutoCheckMileageWriter start...");
        warDao.insertAutoCheckMileage(autoCheckMileagesList);
        log.info("AutoCheckMileageWriter end...{}", autoCheckMileagesList.size());
    }
}

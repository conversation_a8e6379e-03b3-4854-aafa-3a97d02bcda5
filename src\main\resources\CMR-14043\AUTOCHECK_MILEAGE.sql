  CREATE TABLE "SIMCOE"."AUTOCHECK_MILEAGE" 
   (	"AM_ID" NUMBER NOT NULL ENABLE, 
	"AM_ACC_NBR" VARCHAR2(30 BYTE) NOT NULL ENABLE, 
	"CTX_PRODUCT" VARCHAR2(200 BYTE), 
	"CTX_CANCEL_REASON" VARCHAR2(200 BYTE), 
	"CTX_CANCEL_DT" DATE, 
	"LAST_ODO_MILEAGE" NUMBER NOT NULL ENABLE, 
	"LAST_ODO_DATE" DATE NOT NULL ENABLE, 
	"CREATED_BY" VARCHAR2(60 BYTE), 
	"CREATION_DATE" DATE, 
	"LAST_UPDATED_BY" VARCHAR2(60 BYTE), 
	"LAST_UPDATE_DATE" DATE, 
	 CONSTRAINT "AUTOCHECK_MILEAGE_PK" PRIMARY KEY ("AM_ID")
   );

   CREATE SEQUENCE  "SIMCOE"."AM_ID_SEQ"  MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE ;

package com.westlakefinancial.technology.partner.batch.reader;

import com.westlakefinancial.technology.partner.batch.constants.CommonConstant;
import com.westlakefinancial.technology.partner.batch.domain.AutoCheckMileage;
import com.westlakefinancial.technology.partner.batch.domain.BatchCommonDto;
import com.westlakefinancial.technology.partner.batch.domain.ErrorInfo;
import com.westlakefinancial.technology.partner.batch.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.batch.item.ItemReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date ：Created in 2024/2/26 10:26
 */
@Component
@Slf4j
public class AutoCheckMileageReader implements ItemReader<AutoCheckMileage> {

    @Resource
    private BatchCommonDto batchCommonDto;

    @Value("${file.path}")
    private String folderPath;

    private LinkedList<AutoCheckMileage> autoCheckMileageList;

    @Override
    public AutoCheckMileage read() {

        if (null == autoCheckMileageList) {
            log.info("AutoCheckMileageReader start...");
            autoCheckMileageList = new LinkedList<>();
            List<String> filePaths = getAllFilePaths(folderPath);
            if (CollectionUtils.isEmpty(filePaths)) {
                throw new BusinessException("No file found.");
            }
            for (String path : filePaths) {
                settleDocuments(autoCheckMileageList,path);
            }
        }
        if (CollectionUtils.isEmpty(autoCheckMileageList)) {
            return null;
        }
        return autoCheckMileageList.pop();
    }

    private void settleDocuments(LinkedList<AutoCheckMileage> autoCheckMileageList, String path) {
        try (XSSFWorkbook workbook = new XSSFWorkbook(path)) {

            XSSFSheet sheet = workbook.getSheetAt(CommonConstant.PAGE_INDEX_FIRST);
            int lastRowNum = sheet.getLastRowNum();
            if (lastRowNum > 0) {
                for (int rowNum = 1; rowNum <= lastRowNum; rowNum++) {
                    XSSFRow row = sheet.getRow(rowNum);
                    if (Objects.nonNull(row)) {
                        buildAutoCheckMileage(autoCheckMileageList, row);
                    }
                }
            }
            log.info("Retrieved Rows Count: " + autoCheckMileageList.size());
            log.info("AutoCheckMileageReader end...");
        } catch (Exception e) {
            log.error("ReadDetailsXLSX Error occur.", e);
            ErrorInfo errorInfo = new ErrorInfo();
            errorInfo.setErrorStep("autoCheckMileageStep");
            errorInfo.setErrorMsg(e.toString());
            batchCommonDto.getErrorInfos().add(errorInfo);
        }
    }

    private void buildAutoCheckMileage(List<AutoCheckMileage> autoCheckMileageList, XSSFRow row) {
        try {
            if (Boolean.TRUE.equals(row.getCell(CommonConstant.CAN_USE_MILEAGE_INDEX).getBooleanCellValue())) {
                AutoCheckMileage autoCheckMileage = new AutoCheckMileage();
                autoCheckMileage.setAmAccNbr(getCellValue(row.getCell(CommonConstant.ACC_NBR_INDEX)));
                autoCheckMileage.setCtxProduct(getCellValue(row.getCell(CommonConstant.CTX_PRODUCT_INDEX)));
                autoCheckMileage.setCtxCancelReason(getCellValue(row.getCell(CommonConstant.CTX_CANCEL_REASON_INDEX)));
                autoCheckMileage.setCtxCancelDt(getCellValue(row.getCell(CommonConstant.CTX_CANCEL_DT_INDEX)));

                long mileage = Long.parseLong(getCellValue(row.getCell(CommonConstant.LAST_ODO_READING_INDEX)));
                autoCheckMileage.setLastOdoMileage(mileage);
                autoCheckMileage.setLastOdoDate(getCellValue(row.getCell(CommonConstant.LAST_ODO_DATE_INDEX)));

                checkDateFormat(autoCheckMileage);
                if (mileage > 0) {
                    autoCheckMileageList.add(autoCheckMileage);
                }
            }
        } catch (Exception e) {
            log.error("BuildAutoCheckMileage error row: {}", row.getRowNum());
        }
    }

    private void checkDateFormat(AutoCheckMileage autoCheckMileage) {
        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstant.DATE_FORMAT);
        try {
            sdf.parse(autoCheckMileage.getCtxCancelDt());
            sdf.parse(autoCheckMileage.getLastOdoDate());
        } catch (ParseException e) {
            throw new BusinessException("Date format error");
        }
    }

    private String getCellValue(XSSFCell cell) {

        DataFormatter formatter = new DataFormatter();

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstant.DATE_FORMAT);
                        return sdf.format(cell.getDateCellValue());
                    } else {
                        return formatter.formatCellValue(cell);
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                default:
                    return "";
            }
        } catch (Exception e) {
            return "";
        }
    }

    public static List<String> getAllFilePaths(String directoryPath) {
        List<String> filePaths = new ArrayList<>();
        Path startPath = Paths.get(directoryPath);

        try {
            Files.walkFileTree(startPath, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                    filePaths.add(file.toString());
                    return FileVisitResult.CONTINUE;
                }

                @Override
                public FileVisitResult visitFileFailed(Path file, IOException exc) {
                    System.err.println("Failed to access file: " + file.toString() + " (" + exc.getMessage() + ")");
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (IOException e) {
            e.printStackTrace();
        }

        return filePaths;
    }
}

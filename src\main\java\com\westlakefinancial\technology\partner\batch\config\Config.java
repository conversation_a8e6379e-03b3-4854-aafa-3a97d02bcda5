package com.westlakefinancial.technology.partner.batch.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * The type Config.
 *
 * <AUTHOR>
@Configuration
@Slf4j
@Data
@PropertySource(value = "classpath:commonConfig.yml", encoding = "utf-8", factory = YamlPropertySourceFactory.class)
public class Config {
    /**
     * The Local folder path.
     */
    @Value("${mail.scan.local-folder-path}")
    private String localFolderPath;
    /**
     * The Mongo db collection.
     */
    @Value("${mongodb.source-collection}")
    private String mongoDBCollection;

    /**
     * The Save pdf text.
     */
    @Value("${debug.save-pdf-text:false}")
    private Boolean savePdfText;

    /**
     * The specified email IDs used by ProcessSpecifiedEmailJob.
     */
    @Value("${debug.email-ids:}")
    private String emailIds;

    /**
     * The Save pdf text.
     */
    @Value("${debug.save-pdf-text-folder:${mail.scan.local-folder-path}}")
    private String savePdfTextFolder;

    /**
     * The Credentials stream.
     */
    @Value("${file.credentialsStream}")
    private String credentialsStream;

    /**
     * The Project id.
     */
    @Value("${file.projectId}")
    private String projectId;

    /**
     * The Bucket name.
     */
    @Value("${file.bucketName}")
    private String bucketName;

    /**
     * The Bucket folder.
     */
    @Value("${file.bucketFolder}")
    private String bucketFolder;

    /**
     * The Content type.
     */
    @Value("${file.contentType}")
    private String contentType;


}

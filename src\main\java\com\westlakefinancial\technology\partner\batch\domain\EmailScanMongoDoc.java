package com.westlakefinancial.technology.partner.batch.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.bson.types.ObjectId;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * The type Email scan mongo doc.
 *
 * <AUTHOR>
@Data
public class EmailScanMongoDoc {
    /**
     * The id.
     */
    private ObjectId id;
    /**
     * The Properties.
     */
    private EmailEntity properties;
    /**
     * The Email data.
     */
    private EmailData emailData;
    /**
     * The agent id.
     */
    private String agentId;

    /**
     * The type Email entity.
     */
    @Data
    public static class EmailEntity {
        /**
         * The Email id.
         */
        private String emailId;
        /**
         * The Vin.
         */
        private String vin;
//        /**
//         * The Acc nbr.
//         */
//        private String accNbr;
        /**
         * The Received date.
         */
        private String receiveDate;
        /**
         * The Status.
         */
        private String status;
        /**
         * The Ref message id.
         */
        private List<String> refMessageId;
        /**
         * The Message id.
         */
        private String messageId;
        /**
         * The In reply to.
         */
        private String inReplyTo;
        /**
         * The creation date.
         */
        private String creationDate;
        /**
         * The update date.
         */
        private String updateDate;
    }

    /**
     * The type Email data.
     */
    @Data
    public static class EmailData {
        /**
         * The From.
         */
        private String from;
        /**
         * The To.
         */
        private String to;
        /**
         * The Original from.
         */
        private String originalFrom;
        /**
         * The Original to.
         */
        private String originalTo;
        /**
         * The Attachments.
         */
        private Integer attachments;

        /**
         * The Subject.
         */
        private String subject;
        /**
         * The Text.
         */
        private String text;
        /**
         * The Html.
         */
        private String html;
        /**
         * The Attachment info.
         */
        private Map<String, AttachmentInfo> attachmentInfo = new LinkedHashMap<>();
        /**
         * The Body Parsed Data.
         */
        private Map<String, String> bodyParsedData = new LinkedHashMap<>();

        /**
         * The type Attachment info.
         */
        @Data
        @AllArgsConstructor
        public static class AttachmentInfo {
            /**
             * The Filename.
             */
            private String filename;
            /**
             * The Name.
             */
            private String name;
            /**
             * The Id.
             */
            private String id;
            /**
             * The Type.
             */
            private String type;
            /**
             * The Storage path.
             */
            private String storagePath;
            /**
             * The Parsed data.
             */
            private Map<String, String> parsedData;
        }
    }
}



package com.westlakefinancial.technology.partner.batch.tasklet;

import com.westlakefinancial.technology.partner.batch.constants.CommonConstant;
import com.westlakefinancial.technology.partner.batch.domain.CommonEmailInfo;
import com.westlakefinancial.technology.partner.batch.domain.VerificationInfo;
import com.westlakefinancial.technology.partner.batch.facade.AccountServiceFacade;
import com.westlakefinancial.technology.partner.batch.mapper.war.WarDao;
import com.westlakefinancial.technology.partner.batch.service.CommonSendMailService;
import com.westlakefinancial.technology.partner.batch.utils.Counter;
import com.westlakefinancial.technology.partner.batch.utils.JsonUtility;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * @auther bf
 * @date 2024/12/16
 */
@Slf4j
@Component
public class SendWFIEmailToProviderTasklets implements Tasklet {

    @Resource
    private WarDao warDao;

    @Resource(name = "sendCommonMailService")
    private CommonSendMailService sendMailService;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executorService;

    @Value("${mail.verification.host}")
    private String mailSmtpHost;

    @Value("${mail.verification.port}")
    private String mailPort;

    @Value("${mail.verification.wfi.from}")
    private String wfiMailFrom;

    @Value("${mail.verification.wfi.cc}")
    private String mailCc;

    @Value("${mail.verification.retry.count}")
    private int reTryCount;

    @Value("${mail.verification.headers}")
    private String mailHeaders;

    @Value("${mail.verification.test.mode}")
    private String testMode;

    @Value("${mail.verification.test.email}")
    private String testEmail;

    @Value("${mail.verification.resendDays:7}")
    private Integer resendDays;

    @Resource
    private AccountServiceFacade accountServiceFacade;

    private final static String WFI = "C-WFI";

    private final static String WFI_COMPANY = "Western Funding Inc";

    private final static String CREATED_BY = "SIMCOE-WARRANTY-BATCH";
    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) {
        log.info("SendEmailToProviderWriter start...");
        Map<String, String> runParams = Counter.getRunParam();
        String frequencyType = runParams.get("frequencyType");
        LinkedList<VerificationInfo> verificationList = new LinkedList<>();
        if ("daily".equalsIgnoreCase(frequencyType)) {
            verificationList = warDao.getSendEmailInfo(WFI);
        } else if ("weekly".equalsIgnoreCase(frequencyType)) {
            verificationList = warDao.getWeeklySendEmailInfo(WFI,resendDays);
        }
        Map<String, List<VerificationInfo>> groupedByProvider = verificationList.stream()
                .collect(Collectors.groupingBy(VerificationInfo::getProvider));

        List<CompletableFuture<Void>> futures = groupedByProvider.entrySet().stream()
                .map(entry -> CompletableFuture.runAsync(() -> processProvider(entry.getKey(), entry.getValue()), executorService))
                .collect(Collectors.toList());

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allOf.join();

        log.info("SendEmailToProviderWriter end...{}", verificationList.size());
        log.info("Call sendEmailToProviderTasklets end");
        return RepeatStatus.FINISHED;
    }

    private void processProvider(String provider, List<VerificationInfo> providerVerificationList) {
        List<VerificationInfo> backupList = new CopyOnWriteArrayList<>(providerVerificationList);
        backupList.removeIf(verificationInfo -> {
            if (verificationInfo.getNumSent() >= reTryCount) {
                verificationInfo.setDataType("RETRY_TIMES");
                verificationInfo.setErrorResult("Email verification request expired");
                synchronized (warDao) {
                    warDao.insertExceptionQueue(verificationInfo);
                }
                return true;
            }
            synchronized (warDao) {
                warDao.updateSentCount(verificationInfo);
                Map<String, String> notes = new HashMap<>();
                notes.put("acmComment", MessageFormat.format("Sent Warranty Verification - to Provider {0}.", verificationInfo.getProviderEmail()));
                notes.put("acmAadId", verificationInfo.getAadId());
                notes.put("acmCommentBy", CREATED_BY);
                notes.put("acmCommentTypeCd", "SYG");
                notes.put("acmCommentSubTypeCd", "SYS");
                notes.put("acmCommentImpInd", "N");
                notes.put("acmAadOrigSysXref", "UNDEFINED");
                notes.put("acmOrigSysXref", "UNDEFINED");
                notes.put("createdBy", CREATED_BY);
                notes.put("lastUpdatedBy", CREATED_BY);
                notes.put("accNbr", verificationInfo.getAccNbr());

                try {
                    accountServiceFacade.addComment(JsonUtility.objectToJson(notes));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            return false;
        });

        if (backupList.isEmpty()) {
            return;
        }

        File file = generateExcel(backupList);
        String newProviderEmail = backupList.stream()
                .filter(r -> StringUtils.isNotEmpty(r.getProviderEmail()))
                .map(VerificationInfo::getProviderEmail)
                .findAny()
                .orElse(null);

        String sendEmailTo;
        if ("Y".equalsIgnoreCase(testMode)) {
            sendEmailTo = testEmail;
        }else {
            sendEmailTo = newProviderEmail;
        }

        UUID uuid = UUID.randomUUID();
        CommonEmailInfo commonEmailInfo = CommonEmailInfo.builder()
                .emailFrom(wfiMailFrom)
                .emailHost(mailSmtpHost)
                .emailPort(mailPort)
                .emailContent(MessageFormat.format(CommonConstant.VERIFICATION_CONTENT, WFI_COMPANY))
                .emailCc(mailCc)
                .emailTo(sendEmailTo)
                .subject(MessageFormat.format(CommonConstant.VERIFICATION_SUBJECT, provider))
                .file(file)
                .fileName(file.getName())
                .ltrDepartment("WARRANTY")
                .uuid(uuid.toString())
                .build();

        try {
            sendMailService.send(commonEmailInfo);
        } catch (Exception e) {
            log.error("SendEmail Error provider: {}", provider, e);
        }

        synchronized (warDao) {
            warDao.insertLetterData(commonEmailInfo);
            warDao.updateFirstSendInd(backupList);
        }
    }

    public File generateExcel(List<? extends VerificationInfo> verificationInfoList) {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Verification Info");

        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setColor(IndexedColors.WHITE.getIndex());

        CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);
        headerCellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        headerCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
        headerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Row headerRow = sheet.createRow(0);

        String[] headers = mailHeaders.split(",");
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerCellStyle);
        }

        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        int rowNum = 1;
        for (VerificationInfo verificationInfo : verificationInfoList) {
            Row dataRow = sheet.createRow(rowNum++);
            dataRow.createCell(0).setCellValue(verificationInfo.getAccNbr());
            dataRow.createCell(1).setCellValue(verificationInfo.getVin());
            dataRow.createCell(2).setCellValue(verificationInfo.getVehicleYear());
            dataRow.createCell(3).setCellValue(verificationInfo.getVehicleMake());
            dataRow.createCell(4).setCellValue(verificationInfo.getVehicleModel());
            dataRow.createCell(5).setCellValue(verificationInfo.getCusFirstName());
            dataRow.createCell(6).setCellValue(verificationInfo.getCusLastName());
            dataRow.createCell(7).setCellValue(verificationInfo.getActiveDt());
            dataRow.createCell(8).setCellValue(verificationInfo.getContractDt());
            dataRow.createCell(9).setCellValue(verificationInfo.getDealer());
            dataRow.createCell(10).setCellValue(verificationInfo.getDealerCode());
            dataRow.createCell(11).setCellValue(verificationInfo.getDealerState());
            dataRow.createCell(12).setCellValue(verificationInfo.getProvider());
            dataRow.createCell(13).setCellValue(verificationInfo.getPremium());
            dataRow.createCell(14).setCellValue(verificationInfo.getAllDataCorrect());
            dataRow.createCell(15).setCellValue(verificationInfo.getActivated());
            dataRow.createCell(16).setCellValue(verificationInfo.getNotes());
        }


        File tempFile = null;
        try {
            Path tempFilePath = Files.createTempFile("verification_info_", ".xlsx");
            tempFile = tempFilePath.toFile();

            try (FileOutputStream fileOut = new FileOutputStream(tempFile)) {
                workbook.write(fileOut);
            }

        } catch (IOException e) {
            log.error("Generate Excel Error {}", e);
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                log.error("Close Excel Error {}", e);
            }
        }
        return tempFile;
    }
}

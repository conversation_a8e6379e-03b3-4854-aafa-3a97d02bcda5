package com.westlakefinancial.technology.partner.batch.domain;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CancelTxnsDTO {

    private String accNbr;

    private String product;

    private String cancelType;

    private String cancelDate;

    private BigDecimal dealerAmt;

    private BigDecimal premiumAmt;

    private String resultInd;

    private String app;

    private String user;

    private Integer errInd;

    private String errMsg;

}

package com.westlakefinancial.technology.partner.batch.listener;

import com.westlakefinancial.technology.partner.batch.domain.BatchCommonDto;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class JobStepExecutionListener implements StepExecutionListener {

    @Autowired
    private BatchCommonDto batchCommonDto;

    @Override
    public void beforeStep(StepExecution stepExecution) {
        log.info(stepExecution.getStepName() + " start");
    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        if (stepExecution.getStatus() == BatchStatus.FAILED || batchCommonDto.getErrorInfos().size() != 0) {

            return ExitStatus.FAILED;
        }
        log.info(stepExecution.getStepName() + " end...");
        return ExitStatus.COMPLETED;
    }
}

package com.westlakefinancial.technology.partner.batch.tasklet;

import com.westlakefinancial.technology.partner.batch.domain.BatchCommonDto;
import com.westlakefinancial.technology.partner.batch.domain.CommonNum;
import com.westlakefinancial.technology.partner.batch.domain.ErrorInfo;
import com.westlakefinancial.technology.partner.batch.mapper.war.WarDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date ：Created in 2024/4/7 9:53
 */
@Slf4j
@Component
public class InitAccProRelationsTasklets implements Tasklet {

    @Resource
    private BatchCommonDto batchCommonDto;

    @Resource
    private CommonNum commonNum;

    @Resource
    private WarDao warDao;

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) {
        try {
            int sharedVariable = commonNum.getSharedVariable();
            log.info("Call InitAccProRelationsTasklets start {}", sharedVariable);
            warDao.callInitAccProRelation(sharedVariable);
        } catch (Exception e) {
            log.error("call InitAccProRelation error :{}" + e.toString());
            ErrorInfo errorInfo = new ErrorInfo();
            errorInfo.setErrorStep("initAccProRelationsStep");
            errorInfo.setErrorMsg(e.toString());
            batchCommonDto.getErrorInfos().add(errorInfo);
        }
        log.info("Call InitAccProRelationsTasklets end");
        return RepeatStatus.FINISHED;
    }
}

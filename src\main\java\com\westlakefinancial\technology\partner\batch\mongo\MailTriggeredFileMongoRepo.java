package com.westlakefinancial.technology.partner.batch.mongo;

import com.westlakefinancial.technology.partner.batch.domain.MailTriggeredFile;

import java.util.List;

/**
 * The interface Mail triggered file mongo repo.
 *
 * <AUTHOR>
public interface MailTriggeredFileMongoRepo {

    /**
     * Gets all files.
     *
     * @return the all files
     */
    List<MailTriggeredFile> getAllFiles();

    /**
     * Gets unresolved files.
     *
     * @return the unresolved files
     */
    List<MailTriggeredFile> getUnresolvedFiles();

    /**
     * Update file.
     *
     * @param file the file
     */
    void updateFile(MailTriggeredFile file);

}

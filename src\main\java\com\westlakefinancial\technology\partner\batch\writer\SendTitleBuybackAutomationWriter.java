package com.westlakefinancial.technology.partner.batch.writer;

import com.westlakefinancial.technology.partner.batch.domain.enums.TitleBuyBackEnum;
import com.westlakefinancial.technology.partner.batch.utils.JsonUtility;
import org.apache.commons.lang3.StringUtils;
import org.docx4j.Docx4J;
import org.docx4j.openpackaging.exceptions.Docx4JException;
import org.docx4j.openpackaging.io3.Save;
import org.docx4j.wml.Document;
import com.westlakefinancial.technology.partner.batch.domain.*;
import com.westlakefinancial.technology.partner.batch.facade.AccountServiceFacade;
import com.westlakefinancial.technology.partner.batch.mapper.war.WarDao;
import lombok.extern.slf4j.Slf4j;
import org.docx4j.XmlUtils;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.xml.bind.JAXBException;
import java.io.*;
import java.nio.file.Files;
import java.util.*;

@Component
@Slf4j
public class SendTitleBuybackAutomationWriter implements ItemWriter<TitleBuyBackAutomationDTO> {

    @Value("${simcoe-service-command.host}")
    private String simcoeServiceCommonUrl;

    @Resource
    private WarDao warDao;

    @Resource
    private AccountServiceFacade accountServiceFacade;

    @Value("${templateFilePath}")
    private String templateFilePath;

    @Value("${mail.verification.from}")
    private String mailFrom;

    @Value("${testMode}")
    private String testMode;


    @Override
    public void write(List<? extends TitleBuyBackAutomationDTO> titleBuyBackAutomationList) throws Exception {

        getParsingPdfFile(titleBuyBackAutomationList);
    }

    private void getParsingPdfFile(List<? extends TitleBuyBackAutomationDTO> titleBuyBackAutomationList) throws JAXBException, IOException, Docx4JException {
        for (TitleBuyBackAutomationDTO titleBuyBackAutomationDTO : titleBuyBackAutomationList) {
            Date today = new Date();
            Date noLateDate;
            Map<String, String> parsingPdfFileMap = new HashMap<>();
            noLateDate = addDays(today);
            DetailAccountDTO detailAccountList = warDao.getDetailAccountList(titleBuyBackAutomationDTO.getAccNbr());
            Map<String, String> accountBreakdownFromDataBase = getAccountBreakdownFromDataBase(titleBuyBackAutomationDTO.getAccNbr(), titleBuyBackAutomationDTO.getAccAadId());
            parsingPdfFileMap.put(DcConstant.ACCOUNT_NUMBER, titleBuyBackAutomationDTO.getAccNbr());
            parsingPdfFileMap.put(DcConstant.CURRENT_DATE, DcConstant.MMddyyyySlashDateFormat.format(today));
            parsingPdfFileMap.put(DcConstant.DEALER_NAME, titleBuyBackAutomationDTO.getDealerName());
            parsingPdfFileMap.put(DcConstant.CONTRACT_DATE, detailAccountList.getContractDate());
            parsingPdfFileMap.put(DcConstant.CUSTOMER_NAME, detailAccountList.getCustomerName());
            parsingPdfFileMap.put(DcConstant.ASSET_NAME, titleBuyBackAutomationDTO.getVinName());
            parsingPdfFileMap.put(DcConstant.ASSET_VIN, titleBuyBackAutomationDTO.getVin());
            double totalDue = Double.parseDouble(accountBreakdownFromDataBase.get(DcConstant.TOTAL_DUE).replace("$", "").replace(",", ""));
            parsingPdfFileMap.put(DcConstant.TOTAL_AMOUNT, formatDouble(totalDue));
            parsingPdfFileMap.put(DcConstant.NO_LATE_DATE, DcConstant.MMddyyyySlashDateFormat.format(noLateDate));
            parsingPdfFileMap.put(DcConstant.DEALER_CODE, titleBuyBackAutomationDTO.getDealerCode());
            parsingPdfFileMap.put(DcConstant.ACC_AAD_ID, titleBuyBackAutomationDTO.getAccAadId());
            parsingPdfFileMap.put(DcConstant.TEMPLATE_FILE_NAME, DcConstant.FILE_NAME);
            Map<String, Object> dealerDetailData = accountServiceFacade.getDealer(titleBuyBackAutomationDTO.getDealerCode());
            byte[] parsingPdfFile = parsingPdfFile(parsingPdfFileMap);
            getBuyBackConfirm(titleBuyBackAutomationDTO, dealerDetailData, parsingPdfFile, accountBreakdownFromDataBase);
        }
    }

    private void getBuyBackConfirm(TitleBuyBackAutomationDTO titleBuyBackAutomationDTO, Map<String, Object> dealerDetailData, byte[] parsingPdfFile,
                                   Map<String, String> accountBreakdownFromDataBase) throws IOException {
        BuyBackPDFDTO buyBackPDFDTO = new BuyBackPDFDTO();
        buyBackPDFDTO.setIsRefile(TitleBuyBackEnum.ISREFILE.getValue());
        buyBackPDFDTO.setAgentId(TitleBuyBackEnum.ALL.getValue());
        buyBackPDFDTO.setAadId(titleBuyBackAutomationDTO.getAccAadId());
        buyBackPDFDTO.setCompanyType(titleBuyBackAutomationDTO.getCompany());
        buyBackPDFDTO.setDealerCode(titleBuyBackAutomationDTO.getDealerCode());
        buyBackPDFDTO.setBuybackType(TitleBuyBackEnum.RETITLE.getValue());
        buyBackPDFDTO.setCalculationType(TitleBuyBackEnum.CALCULATIONTYPE.getValue());
        buyBackPDFDTO.setNetAmtDue(TitleBuyBackEnum.NERAMTDUE.getValue());
        buyBackPDFDTO.setDualBuybackType(TitleBuyBackEnum.DUALBUYBACKTYPE.getValue());
        buyBackPDFDTO.setPdfData(parsingPdfFile);
        buyBackPDFDTO.setSelectedPartner(dealerDetailData);
        Map<String, String> account = new HashMap<>();
        account.put(DcConstant.ACC_NBR, titleBuyBackAutomationDTO.getAccNbr());
        buyBackPDFDTO.setDetailAccount(account);
        buyBackPDFDTO.setAadId(titleBuyBackAutomationDTO.getAccAadId());
        buyBackPDFDTO.setBuybackBreakdownMap(accountBreakdownFromDataBase);
        Map<String, String> userInfo = new HashMap<>();
        userInfo.put(DcConstant.FULL_NAME, DcConstant.TITLE_AUTO_BATCH);
        userInfo.put(DcConstant.EMAIL, mailFrom);
        userInfo.put(DcConstant.ROLE_NAME, DcConstant.DC_AGENT);
        buyBackPDFDTO.setUserInfo(userInfo);
        List<String> dealerCodeEmailList = warDao.getEmailList(titleBuyBackAutomationDTO.getDealerCode());
        buyBackPDFDTO.setEmailList(dealerCodeEmailList);
        buyBackPDFDTO.setAgentEmail(warDao.emailCC());
        buyBackPDFDTO.setTitleAutoBatchFlag(DcConstant.TITLE_AUTO_BATCH_FLAG);
        String jsonStr = JsonUtility.objectToJson(buyBackPDFDTO);
        if (StringUtils.equals(testMode, "N")) {
            log.info("The contents to be sent are as follows ： {}", jsonStr);
            accountServiceFacade
                    .getBuyBackConfirm(simcoeServiceCommonUrl + "dc/account/buyBackConfirm", jsonStr);
        }
    }

    public Map<String, String> getAccountBreakdownFromDataBase(String accNbr, String aadId) {

        Map<String, String> calculatedAmountMap = new LinkedHashMap<>();

        double totalAmt;

        double serviceOtherFee;
        double unpaidPrincipal;
        double interest;
        double earnedInterest;
        double unearnedInsurance;
        double recoverFee;

        Map<String, Object> accountBreakdown = accountServiceFacade.getAccountBreakdown(accNbr, aadId);

        PayoffDataType payoffQuote = accountServiceFacade.getAccountPayoff(accNbr);

        double itmParticipation = Double.parseDouble(accountBreakdown.get("itm_participation").toString());
        double itmPromotion = Double.parseDouble(accountBreakdown.get("itm_promotion").toString());

        double itmFlooring = Double.parseDouble(accountBreakdown.get("itm_flooring").toString());
        double itmCag = Double.parseDouble(accountBreakdown.get("itm_cag").toString());
        if (null == payoffQuote) {
            serviceOtherFee = 100.00 +
                    itmParticipation +
                    itmPromotion + itmFlooring + itmCag;

            unpaidPrincipal = 0.0;
            interest = 0.0;
            earnedInterest = 0.0 + interest;

            unearnedInsurance = 0.0;

            //B17+B18+B19+B20
            recoverFee = 0.0;
        } else {
            serviceOtherFee = 100.00 +
                    Double.parseDouble(payoffQuote.getFeeConvenience()) +
                    Double.parseDouble(payoffQuote.getFeeExtension()) +
                    Double.parseDouble(payoffQuote.getFeeLateCharge()) +
                    Double.parseDouble(payoffQuote.getFeeNsf()) +
                    Double.parseDouble(payoffQuote.getFeePhonePay()) +
                    itmParticipation +
                    itmPromotion + itmFlooring + itmCag;

            unpaidPrincipal = Double.parseDouble(payoffQuote.getAdvancePrinciple());
            interest = Double.parseDouble(payoffQuote.getInterest());
            earnedInterest = Double.parseDouble(payoffQuote.getInterestAccrued()) + interest;

            unearnedInsurance = Double.parseDouble(payoffQuote.getRebate());

            //B17+B18+B19+B20
            recoverFee = Double.parseDouble(payoffQuote.getExpenseLegal()) +
                    Double.parseDouble(payoffQuote.getExpenseRepo()) +
                    Double.parseDouble(payoffQuote.getExpenseDMV()) +
                    Double.parseDouble(payoffQuote.getExpenseDeferred());
        }

        totalAmt = unpaidPrincipal + earnedInterest + unearnedInsurance + recoverFee + serviceOtherFee;

        calculatedAmountMap.put(DcConstant.UNPAID_PRINCIPAL, DcConstant.formatDouble(unpaidPrincipal));
        calculatedAmountMap.put(DcConstant.EARNED_INTEREST, DcConstant.formatDouble(earnedInterest));
        calculatedAmountMap.put(DcConstant.UNEARNED_INSURANCE, DcConstant.formatDouble(unearnedInsurance));
        calculatedAmountMap.put(DcConstant.RECOVERY_FEES, DcConstant.formatDouble(recoverFee));
        calculatedAmountMap.put(DcConstant.SERVICING_OTHER_FEES, DcConstant.formatDouble(serviceOtherFee));
        calculatedAmountMap.put(DcConstant.TOTAL_DUE, DcConstant.formatDouble(totalAmt));


        return calculatedAmountMap;
    }

    private Date addDays(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, 7); //minus number would decrement the days
        return cal.getTime();
    }

    public byte[] parsingPdfFile(Map<String, String> resultMap) throws IOException, Docx4JException, JAXBException {

        byte[] buffer = new byte[0];
        String templateName;
        if (resultMap != null) {
            templateName = resultMap.get(DcConstant.TEMPLATE_FILE_NAME);
            InputStream is;

            File initialFile = new File(templateFilePath + templateName);
            is = Files.newInputStream(initialFile.toPath());

            WordprocessingMLPackage wordMLPackage;
            wordMLPackage = WordprocessingMLPackage.load(is);
            MainDocumentPart part = wordMLPackage.getMainDocumentPart();
            String template = XmlUtils.marshaltoString(part.getJaxbElement());
            for (Map.Entry<String, String> entry : resultMap.entrySet()) {
                template = template.replace(entry.getKey(), entry.getValue());
            }
            Object obj = XmlUtils.unmarshalString(template);
            part.setJaxbElement((Document) obj);

            ByteArrayOutputStream os = new ByteArrayOutputStream();
            Save saver = new Save(wordMLPackage);
            saver.save(os);

            byte[] out = os.toByteArray();
            String timeStamp = DcConstant.yyyyMMddHHmmssDateFormat.format(new Date());
            String fileName = "DcLetter_" + resultMap.get(DcConstant.ACCOUNT_NUMBER) + "_" + timeStamp + ".pdf";
            File tmpFile = new File(templateFilePath + fileName);
            OutputStream outputStream = Files.newOutputStream(tmpFile.toPath());
            WordprocessingMLPackage wordprocessingMLPackage = WordprocessingMLPackage.load(new ByteArrayInputStream(out));
            Docx4J.toPDF(wordprocessingMLPackage, outputStream);

            FileInputStream fis = new FileInputStream(tmpFile);
            ByteArrayOutputStream baos = new ByteArrayOutputStream(fis.available());
            byte[] bytes = new byte[fis.available()];
            int temp;
            while ((temp = fis.read(bytes)) != -1) {
                baos.write(bytes, 0, temp);
            }
            fis.close();
            baos.close();

            buffer = baos.toByteArray();
            if (StringUtils.equals(testMode, "N")) {
                tmpFile.delete();
            }
            os.close();
        }
        return buffer;
    }

    public static String formatDouble(double d) {
        return convertCurrency(String.format("%.2f", d));
    }

    public static String convertCurrency(String amount) {
        return DcConstant.convertCurrency(amount);
    }

}

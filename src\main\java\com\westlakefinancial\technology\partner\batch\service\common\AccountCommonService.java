package com.westlakefinancial.technology.partner.batch.service.common;

import java.security.NoSuchAlgorithmException;
import java.util.Map;

public interface AccountCommonService {
    Map<String, String> sendCallActivity(Map<String, String> params) throws NoSuchAlgorithmException, IllegalAccessException;

    Map<String, String> sendComment(Map<String, String> params) throws NoSuchAlgorithmException, IllegalAccessException;

}

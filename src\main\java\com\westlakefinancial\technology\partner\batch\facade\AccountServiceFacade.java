package com.westlakefinancial.technology.partner.batch.facade;

import com.alibaba.fastjson.JSONObject;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.westlakefinancial.technology.commonUtil.util.OauthUtil;
import com.westlakefinancial.technology.commonUtil.util.RestHttpsUtil;
import com.westlakefinancial.technology.partner.batch.domain.PayoffDataType;
import com.westlakefinancial.technology.partner.batch.exception.BusinessException;
import com.westlakefinancial.technology.partner.batch.utils.RestUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import java.lang.reflect.Type;
import java.util.*;

@Service
@Slf4j
public class AccountServiceFacade {

    private static final String END_LOG_FOR_RETURN = "{} return value={}";
    private static final long TIMEOUT_BUFFER_MS = 10000;

    @Value("${oauth2.tokenUrl}")
    String oauth2URL;
    @Value("${oauth2.username}")
    String username;
    @Value("${oauth2.password}")
    String password;

    @Value("${oauth2.account-service}")
    String denaliAccountURL;

    @Value("${oauth2.payoff-service}")
    String payOffServiceURL;

    @Value("${oauth2.account-misc-service}")
    String accountMiscServiceURL;

    @Value("${oauth2.dealer-service}")
    String denaliDealerURL;

    @Value("${daybreak-common.url}")
    String daybreakCommonURL;

    private String lastDenaliToken;

    private final RestTemplate restTemplate;
    private long tokenExpireTime = System.currentTimeMillis();

    public AccountServiceFacade(RestTemplateBuilder restSimcoeServiceCommand) {
        this.restTemplate = restSimcoeServiceCommand.build();
    }

    public LinkedHashMap<String, String> getBuyBackConfirm(String url, String param) {

        LinkedHashMap<String, String> result = (LinkedHashMap<String, String>) restTemplate.postForObject(url, param, Object.class);
        return result;
    }

//    public LinkedHashMap<String, String> getBuyBackConfirm(String url, String param) {
//
//        // Make sure the URL uses the http protocol instead of https
//        url = url.replace("https://", "http://");
//
//        LinkedHashMap<String, String> result = (LinkedHashMap<String, String>) restTemplate.postForObject(url, param, Object.class);
//        return result;
//    }


    public String getDenaliToken() {
        String denaliToken = OauthUtil.getOauthToken(oauth2URL, username, password);
        Type type = new TypeToken<Map<String, String>>() {
        }.getType();
        Gson gson = new GsonBuilder().create();
        Map<String, String> json = gson.fromJson(denaliToken, type);

        String tokenTimeout = json.get("expires_in");
        if (!StringUtils.isEmpty(tokenTimeout)) {
            try {
                tokenExpireTime =
                        System.currentTimeMillis() + (Long.parseLong(tokenTimeout) * 1000) - TIMEOUT_BUFFER_MS;
            } catch (Exception e) {
                log.error("Failed to get denali token timeout.");
            }
        }

        return json.get("access_token");
    }

    public String getToken() {
        // check if timeout
        if ((System.currentTimeMillis() >= tokenExpireTime) || StringUtils.isEmpty(lastDenaliToken)) {
            lastDenaliToken = getDenaliToken();
        }

        if (StringUtils.isEmpty(lastDenaliToken)) {
            log.info("Denali Token is null");
        }

        return lastDenaliToken;
    }

    public Map<String, Object> getAccountBreakdown(String accNbr, String aadId){

        String accountUrl = denaliAccountURL + "/accounts/v1/accountList?company&infos=balance&accNbrs=" + accNbr;
        String account = RestHttpsUtil.send(accountUrl, getToken(), "GET");

        log.info(END_LOG_FOR_RETURN, "getAccountBreakdown account = ", account);
        List<Map>  accMapList = JSONObject.parseArray(account, Map.class);

        Map accMap = null;
        if (accMapList != null && accMapList.get(0) != null) {
            accMap = accMapList.get(0);
        }
        if (accMap == null) {
            throw new BusinessException("Call denali exception: getAccountBreakdown account");
        }

        String getAccountTrackingAttributesUrl = denaliAccountURL + "/accounts/v1/getAccountTrackingAttributes/" + aadId;
        String accountTrackingAttributes = RestHttpsUtil.send(getAccountTrackingAttributesUrl, getToken(), "GET");

        log.info(END_LOG_FOR_RETURN, "getAccountBreakdown accountTrackingAttributes = ", accountTrackingAttributes);

        List<Map> attList = null;
        if (StringUtils.isNotEmpty(accountTrackingAttributes)) {
            attList = JSONObject.parseArray(accountTrackingAttributes, Map.class);
        }

        if (attList == null) {
            attList = new ArrayList<>();
        }

        Map contractMap = getContractDetail(aadId);

        List<Map> contractList = null;
        if (contractMap.get(aadId) != null) {
            contractList = (List<Map>) contractMap.get(aadId);
        }

        if (contractList == null) {
            throw new BusinessException("Call denali exception: getAccountBreakdown contracts");
        }

        String accountScoringUrl = accountMiscServiceURL + "/accountMisc/v1/" + accNbr + "/getAccountScoring";
        String accountScoring = RestHttpsUtil.send(accountScoringUrl, getToken(), "GET");

        log.info(END_LOG_FOR_RETURN, "getAccountBreakdown accountScoring = ", accountScoring);

        Map<String, Object> accScoringMap = JSONObject.parseObject(accountScoring, Map.class);
        if (accScoringMap == null) {
            accScoringMap = new HashMap<>();
        }

        Map map = buildAccountBreakdownData(accMap, accScoringMap, contractList, attList);
        return map;
    }

    public PayoffDataType getAccountPayoff(String accNbr){

        String url = payOffServiceURL + "/payOff/v1/getAccountPayoff/" + accNbr;
        String result = RestUtils.send(url, "", getToken(), "POST");

        log.info(END_LOG_FOR_RETURN, "getAccountPayoff = ", result);

        PayoffDataType payoffDataType = JSONObject.parseObject(result, PayoffDataType.class);

        return payoffDataType;
    }

    public Map getContractDetail (String aadId) {

        Map<String,List<Map>> contractListMap = new HashMap<>();
        String contractsUrl = accountMiscServiceURL + "/accountMisc/v1" + "/contract_detail?aadIdList=" + aadId;
        String contracts = RestHttpsUtil.send(contractsUrl, getToken(), "GET");
        if (StringUtils.isNotEmpty(contracts)) {
            contractListMap = (Map<String,List<Map>>)JSONObject.parseObject(contracts, Map.class);
        }
        log.info(END_LOG_FOR_RETURN, "getAccountBreakdown contracts = ", contracts);
        return contractListMap;
    }

    public Map<String, Object> getDealer(String dealerCode) {
        Map<String, Object> dealerDetailData = null;
        String url = denaliDealerURL + "/dealer/v1/" + dealerCode + "/dealer_detail";

        String jsonStr = RestHttpsUtil.send(url, getToken(), "GET");
        log.info(END_LOG_FOR_RETURN, "getDealer", jsonStr);
        if (null == jsonStr || jsonStr.isEmpty()) return null;
        dealerDetailData = (Map<String, Object>) JSONObject.parse(jsonStr);

        return dealerDetailData;
    }

    private Map buildAccountBreakdownData(Map<String, Object> accMap, Map<String, Object> accScoringMap, List<Map> contractList,
                                          List<Map> attList) {


        Map balance = new HashMap();
        if (accMap.get("balance") != null) {
            balance = (Map) accMap.get("balance");
        }
        double accAmtFinanced = accMap.get("amtFinanced") == null ?
                0 : Double.valueOf(String.valueOf(accMap.get("amtFinanced")));

        double advAblBal = balance.get("advAblBal") == null ?
                0 : Double.valueOf(String.valueOf(balance.get("advAblBal")));
        double advAblBalPd = balance.get("advAblBalPd") == null ?
                0 : Double.valueOf(String.valueOf(balance.get("advAblBalPd")));

        double intAblBalPd = balance.get("intAblBalPd") == null ?
                0 : Double.valueOf(String.valueOf(balance.get("intAblBalPd")));

        double total_amt_paid = advAblBalPd + intAblBalPd;

        double deficiencyBalance = balance.get("deficiencyBalance") == null ?
                0 : Double.valueOf(String.valueOf(balance.get("deficiencyBalance")));

        accMap.put("total_amt_paid", total_amt_paid);
        accMap.put("deficiencyBalance", deficiencyBalance);

        double particiation_ptc = accScoringMap.get("asiParticipationPercent") == null ?
                0 : Double.valueOf(String.valueOf(accScoringMap.get("asiParticipationPercent")));

        accMap.put("acc_nbr", accMap.get("accNbr"));
        accMap.put("acc_amt_financed", accAmtFinanced);
        accMap.put("acc_contract_dt", accMap.get("contractDate"));
        accMap.put("acc_term", accMap.get("orgTerm"));
        accMap.put("acc_pmt_amt", accMap.get("pmtAmt"));

        double itm_acquisition_fee = getContractFee(contractList, "FUP_1", "IUP_1" );
        double itm_cash_sales = getContractFee(contractList, "FUN_1", "IUN_3" );
        double itm_discount = getContractFee(contractList, "FUP_6", "IUP_6" );
        double itm_cash_sales_tax = getContractFee(contractList, "FUN_1", "ITM_CSH_SALES_TAX" );
        double itm_withhold = getContractFee(contractList, "FUP_5", "IUP_5" );
        double itm_down_payment = getContractFee(contractList, "FUN_1", "IDC_1" );
        double itm_florida_doc_stamp_fee = getContractFee(contractList, "FUP_2", "IUP_2" );
        double itm_down_payment_tradein = getContractFee(contractList, "FUN_1", "IDN_3" );
        double itm_reserve = getContractFee(contractList, "FUP_7", "IUP_7" );
        double itm_down_payment_payoff = getContractFee(contractList, "FUN_1", "IDN_1" );
        double itm_gap_waiver1 = getContractFee(contractList, "FIN_4", "IIN_4" );
        double itm_participation = getContractFee(contractList, "FUP_11", "IUP_11" );
        double itm_smog_fee = getContractFee(contractList, "FUN_1", "IUN_4" );
        double itm_insurance = getContractFee(contractList, "FUP_10", "IUP_10" );
        double itm_dealer_doc_fee = getContractFee(contractList, "FUN_1", "IUN_2" );
        double itm_gap_waiver2 = getContractFee(contractList, "FUP_8", "IUP_8" );
        double itm_waranty_cost = getContractFee(contractList, "FUP_9", "IUP_9" );
        double itm_service_contract_retail = getContractFee(contractList, "FWA_1", "IWA_1" );
        double itm_dealer_pmts = getContractFee(contractList, "FUP_12", "IUP_12" );
        double itm_contract_fee = getContractFee(contractList, "FUN_1", "IUN_6" );
        double itm_registration_fee = getContractFee(contractList, "FUN_1", "IUN_5" );
        double itm_dealer_proceeds = getContractFee(contractList, "FUP_13", "IUP_13" );
        double itm_3rd_party_gap = getContractFee(contractList, "FUN_1", "IUN_7" );
        double itm_dmv_fees_held = getContractFee(contractList, "FUP_14", "IUP_14" );
        double itm_fpd_waiver = getContractFee(contractList, "FUP_15", "IUP_15" );
        double itm_finananced_auto_insurance = getContractFee(contractList, "FIN_5", "IIN_5" );
        double itm_promotion = getContractFee(contractList, "FUP_16", "IUP_16" );
        double itm_bonus_pool = getContractFee(contractList, "FUP_18", "IUP_21" );
        double itm_flooring = getContractFee(contractList, "FUP_21", "IUP_24" );
        double itm_cag = getContractFee(contractList, "FUP_22", "IUP_18" );

        accMap.put("particiation_ptc", particiation_ptc);
        accMap.put("itm_acquisition_fee", itm_acquisition_fee);
        accMap.put("itm_cash_sales", itm_cash_sales);
        accMap.put("itm_discount", itm_discount);
        accMap.put("itm_cash_sales_tax", itm_cash_sales_tax);
        accMap.put("itm_withhold", itm_withhold);
        accMap.put("itm_down_payment", itm_down_payment);
        accMap.put("itm_florida_doc_stamp_fee", itm_florida_doc_stamp_fee);
        accMap.put("itm_down_payment_tradein", itm_down_payment_tradein);
        accMap.put("itm_reserve", itm_reserve);
        accMap.put("itm_down_payment_payoff", itm_down_payment_payoff);
        accMap.put("itm_gap_waiver1", itm_gap_waiver1);
        accMap.put("itm_participation", itm_participation);
        accMap.put("itm_smog_fee", itm_smog_fee);
        accMap.put("itm_insurance", itm_insurance);
        accMap.put("itm_dealer_doc_fee", itm_dealer_doc_fee);
        accMap.put("itm_gap_waiver2", itm_gap_waiver2);
        accMap.put("itm_waranty_cost", itm_waranty_cost);
        accMap.put("itm_service_contract_retail", itm_service_contract_retail);
        accMap.put("itm_dealer_pmts", itm_dealer_pmts);
        accMap.put("itm_contract_fee", itm_contract_fee);
        accMap.put("itm_registration_fee", itm_registration_fee);
        accMap.put("itm_dealer_proceeds", itm_dealer_proceeds);
        accMap.put("itm_3rd_party_gap", itm_3rd_party_gap);
        accMap.put("itm_dmv_fees_held", itm_dmv_fees_held);
        accMap.put("itm_fpd_waiver", itm_fpd_waiver);
        accMap.put("itm_finananced_auto_insurance", itm_finananced_auto_insurance);
        accMap.put("itm_promotion", itm_promotion);
        accMap.put("itm_bonus_pool", itm_bonus_pool);
        accMap.put("itm_flooring", itm_flooring);
        accMap.put("itm_cag", itm_cag);

        double totalDue = getAccountPNBD(contractList, itm_dealer_proceeds, accAmtFinanced, advAblBal, advAblBalPd, particiation_ptc);
        accMap.put("Total Due", totalDue);

        return accMap;

    }

    private double getAccountPNBD (List<Map> contractList, double itmAcdVal, double accAmtFinanced,
                                   double advBl, double advBlPd, double asi_percent) {
        double sum = 0;
        double totalDue = 0;

        for (Map p : contractList) {

            double acdSign = Double.valueOf(String.valueOf(p.get("acdSign")));
            double acdVal = Double.valueOf(String.valueOf(p.get("acdVal")));

            sum = sum + acdSign * acdVal;
        }

        totalDue = sum + itmAcdVal - (accAmtFinanced - (advBl - advBlPd)) * (1 - asi_percent/100);
        if (totalDue < 0) {
            return 0.00;
        }

        return (double)Math.round(totalDue * 100)/100;

    }

    private double getContractFee (List<Map> contractList, String tcdCode, String itmTcdCode) {

        double contractFee = 0 ;

        for (Map p : contractList) {

            if (tcdCode.equals(p.get("acdTcdCode")) && itmTcdCode.equals(p.get("acdItemizationTcdCode"))) {

                contractFee = p.get("acdVal") == null ? 0 : Double.valueOf(String.valueOf(p.get("acdVal")));
            }

        }

        return contractFee;

    }

    public LinkedHashMap<String, String> addComment(String body) {
        String url = daybreakCommonURL + "/common/addComment";
        LinkedHashMap<String, String> result = (LinkedHashMap<String, String>) restTemplate.postForObject(url, body, Object.class);
        return result;
    }

}

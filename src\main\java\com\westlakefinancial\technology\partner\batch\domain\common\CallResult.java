package com.westlakefinancial.technology.partner.batch.domain.common;

import lombok.Data;

import java.util.Date;

@Data
public class CallResult {
    private long id; //rst_id
    private String cd; //rst_cd
    private long actionId; //rst_act_id
    private Date creationDate; //rst_creation_dt
    private String enabled; //rst_enabled
    private String description; //rst_description
    private String objectLevel; //rst_object_level
    private String triggerDbk; //rst_trigger_dbk
    private String dbkActionCd; //rst_dbk_action_cd
    private String dbkResultCd; // rst_dbk_result_cd
    private String followUpDays; // rst_folllowup_days

    @Override
    public boolean equals(Object o) {
        if (o == null) {
            return false;
        }
        if (!(o instanceof CallResult)) {
            return false;
        }
        CallResult c = (CallResult)o;
        return c.getId() == id;
    }
}


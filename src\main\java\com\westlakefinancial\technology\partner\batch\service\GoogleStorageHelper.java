package com.westlakefinancial.technology.partner.batch.service;

import com.google.auth.Credentials;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.storage.*;
import com.westlakefinancial.technology.partner.batch.config.Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * The type Google storage helper.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "debug.mock.storage", havingValue = "false", matchIfMissing = true)
public class GoogleStorageHelper implements StorageHelper {

    /**
     * The Config.
     */
    private final Config config;

    /**
     * The Storage.
     */
    private Storage storage;

    /**
     * Instantiates a new Google storage helper.
     *
     * @param config the config
     */
    public GoogleStorageHelper(Config config) {
        this.config = config;
    }

    @Override
    public void upload(String filePath, String targetPath) throws IOException {
        File dest = new File(filePath);
        if (!dest.exists()) {
            throw new IllegalStateException("File not found:" + filePath);
        }

        BlobId blobId = BlobId.of(config.getBucketName(), targetPath);
        Blob blob = storage.get(blobId);
        if (blob != null) {
            blob.delete();
        }
        BlobInfo blobInfo = BlobInfo.newBuilder(blobId).setContentType(config.getContentType()).build();
        storage.create(blobInfo, Files.readAllBytes(Paths.get(filePath)));
    }

    @Override
    public void delete(String targetPath) {
        BlobId blobId = BlobId.of(config.getBucketName(), targetPath);
        Blob blob = storage.get(blobId);
        if (blob != null) {
            blob.delete();
        }
    }

    @Override
    public void init() throws IOException {
        Credentials credentials = GoogleCredentials.fromStream(new FileInputStream(config.getCredentialsStream()));
        this.storage = StorageOptions.newBuilder().setCredentials(credentials).setProjectId(config.getProjectId()).build().getService();
        log.info("[->] Google storage is ready");
    }
}

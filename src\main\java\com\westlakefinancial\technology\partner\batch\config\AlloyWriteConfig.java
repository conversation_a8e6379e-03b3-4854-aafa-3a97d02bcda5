package com.westlakefinancial.technology.partner.batch.config;

import com.westlakefinancial.technology.partner.batch.exception.BusinessException;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {"com.westlakefinancial.technology.partner.batch.mapper.alloy.write"},
        sqlSessionFactoryRef = "alloyWriteSqlSessionFactory")
@PropertySource(value = "classpath:commonConfig.yml", encoding = "utf-8", factory = YamlPropertySourceFactory.class)
public class AlloyWriteConfig {
    @Value("${datasource.alloy.write.url}")
    private String consumerUrl;

    @Value("${datasource.alloy.write.username}")
    private String consumerUserName;

    @Value("${datasource.alloy.write.password}")
    private String consumerPassword;

    @Value("${datasource.alloy.write.driverClassName}")
    private String consumerDriver;

    @Bean(name = "alloyWriteDataSource")
    public DataSource getDateSource() {
        HikariConfig config = new HikariConfig();

        // Set the configuration properties
        config.setDriverClassName(consumerDriver);
        config.setJdbcUrl(consumerUrl);
        config.setUsername(consumerUserName);
        config.setPassword(consumerPassword);

        // HikariCP configuration properties
        config.setMaximumPoolSize(30);
        config.setMinimumIdle(5);
        config.setIdleTimeout(180000);
        config.setConnectionTimeout(60000);
        config.setMaxLifetime(1200000);

        // Create the DataSource
        return new HikariDataSource(config);
    }
    /**
     * generate assignmentSqlSessionFactory.
     *
     * @return SqlSessionFactory
     */
    @Bean(name = "alloyWriteSqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("alloyWriteDataSource") DataSource datasource)
            throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(datasource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources("classpath*:com/westlakefinancial/technology/partner/batch/"
                        + "mapper/alloy/write/*.xml"));
        SqlSessionFactory sqlSessionFactory = bean.getObject();
        if(Objects.isNull(sqlSessionFactory)){
            throw new BusinessException("SqlSessionFactory is null");
        }
        sqlSessionFactory.getConfiguration().setJdbcTypeForNull(JdbcType.NULL);
        sqlSessionFactory.getConfiguration().setCallSettersOnNulls(true);
        return sqlSessionFactory;
    }

    @Bean(name = "alloyWriteSqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(
            @Qualifier("alloyWriteSqlSessionFactory") SqlSessionFactory sessionFactory) {
        return new SqlSessionTemplate(sessionFactory);
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westlakefinancial.technology.partner.batch.mapper.liv.DaybreakLivDao">

    <select id="callSecureOneDealerCredit" statementType="CALLABLE" parameterType="com.westlakefinancial.technology.partner.batch.domain.CancelTxnsDTO" >
        { call WFS_SECUREONE_DEALER_CREDIT(
                #{accNbr},
                #{product},
                #{cancelType},
                #{cancelDate},
                #{dealerAmt},
                #{premiumAmt},
                #{app},
                #{user},
                #{errInd,mode=OUT, jdbcType=NUMERIC },
                #{errMsg,mode=OUT, jdbcType=VARCHAR }) }
    </select>
</mapper>

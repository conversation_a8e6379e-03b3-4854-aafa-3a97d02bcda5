package com.westlakefinancial.technology.partner.batch.job;

import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.JobParametersIncrementer;

/**
 * <AUTHOR>
 */
public class TimestampIncrementer implements JobParametersIncrementer {
    @Override
    public JobParameters getNext(JobParameters jobParameters) {
        return new JobParametersBuilder().addLong("time", System.currentTimeMillis()).toJobParameters();
    }
}

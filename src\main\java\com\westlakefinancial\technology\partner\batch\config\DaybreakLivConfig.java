package com.westlakefinancial.technology.partner.batch.config;

import com.westlakefinancial.technology.partner.batch.exception.BusinessException;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {"com.westlakefinancial.technology.partner.batch.mapper.liv"},
        sqlSessionFactoryRef = "daybreakLivSqlSessionFactory")
@PropertySource(value = "classpath:commonConfig.yml", encoding = "utf-8", factory = YamlPropertySourceFactory.class)
public class DaybreakLivConfig {
    @Value("${datasource.oracleDaybreakLivdb.url}")
    private String consumerUrl;

    @Value("${datasource.oracleDaybreakLivdb.username}")
    private String consumerUserName;

    @Value("${datasource.oracleDaybreakLivdb.password}")
    private String consumerPassword;

    @Value("${datasource.oracleDaybreakLivdb.driverClassName}")
    private String consumerDriver;

    @Bean(name = "daybreakLivDataSource")
    public DataSource getDateSource() {
        return DataSourceBuilder.create().driverClassName(consumerDriver)
                .username(consumerUserName)
                .password(consumerPassword)
                .url(consumerUrl)
                .build();
    }
    /**
     * generate assignmentSqlSessionFactory.
     *
     * @return SqlSessionFactory
     */
    @Bean(name = "daybreakLivSqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("daybreakLivDataSource") DataSource datasource)
            throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(datasource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources("classpath*:com/westlakefinancial/technology/partner/batch/"
                        + "mapper/liv/*.xml"));
        SqlSessionFactory sqlSessionFactory = bean.getObject();
        if(Objects.isNull(sqlSessionFactory)){
            throw new BusinessException("SqlSessionFactory is null");
        }
        sqlSessionFactory.getConfiguration().setJdbcTypeForNull(JdbcType.NULL);
        sqlSessionFactory.getConfiguration().setCallSettersOnNulls(true);
        return sqlSessionFactory;
    }

    @Bean(name = "livDaybreakSqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(
            @Qualifier("daybreakLivSqlSessionFactory") SqlSessionFactory sessionFactory) {
        return new SqlSessionTemplate(sessionFactory);
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westlakefinancial.technology.partner.batch.mapper.alloy.read.AlloyReadDao">
    <select id="getAncillaryProductInfo" resultType="com.westlakefinancial.technology.partner.batch.domain.AncillaryProductInfo">
        SELECT
            to_char(now(), 'Month DD,YYYY') as date,
            ctx.ctx_acc_nbr AS accnbr,
            cus.cus_first_name AS firstname,
            cus.cus_last_name AS lastname,
            ase.ase_identification_nbr AS vin,
            TO_CHAR(ctx.ctx_cancel_dt,'MM/DD/YYYY') AS eventdt,
            ctx.ctx_cancel_reason AS eventtype,
            ctx.ctx_cancel_mile  AS mileage,
            ctx.ctx_contract_id::integer AS contractId,
            TO_CHAR(COALESCE(ctx.CTX_ACTUAL_REFUND_AMT, ctx.CTX_EST_REFUND_AMT, 0), 'FM9999999990.00') AS estimatedRefundDue,
            addr.ADR_ADDRESS1 AS address1,
            addr.ADR_ADDRESS2 AS address2,
            addr.adr_city AS city,
            addr.adr_state_cd AS state,
            addr.adr_zip AS zip,
            ctx_product as product
        FROM
            simcoe_warranty.CANCEL_TXNS ctx
                JOIN daybreak.CUS_ACC_RELATIONS car
                     ON car.CAR_AAD_ID = ctx.CTX_AAD_ID
                JOIN daybreak.CUSTOMERS cus
                     ON cus.CUS_ID = car.CAR_CUS_ID
                LEFT JOIN daybreak.ASSETS ase
                          ON ase.ASE_AAD_ID = ctx.CTX_AAD_ID
                              AND ase.ASE_PRIMARY_IND = 'Y'
                LEFT JOIN LATERAL (
                SELECT
                    ADR_ADDRESS1,
                    ADR_ADDRESS2,
                    adr_city,
                    adr_zip,
                    adr_state_cd
                FROM
                    daybreak.address
                WHERE
                    ADR_CURRENT_IND = 'Y'
                  AND ADR_MAILING_IND= 'Y'
                  AND ADR_CUS_ID = car.CAR_CUS_ID
                ORDER BY
                    LAST_UPDATE_DATE DESC
                    LIMIT 1
                ) addr ON true
        WHERE
            car.CAR_RELATION_TYPE_CD = 'PRIM'
          AND car.CAR_ENABLED_IND = 'Y'
          AND ctx.CTX_STATUS NOT IN (
                                     'INELIGIBLE_WFI',
                                     'INELIGIBLE_BUYBACK',
                                     'INELIGIBLE_NO_REFUND_AMT',
                                     'RESOLVED',
                                     'RESOLVED_DEALER'
            )
          AND ctx.CTX_CANCEL_REASON != 'BUYBACK'
          <!-- AND ctx.CTX_PRODUCT NOT IN ('SECURE ONE', 'GAP')
          AND ctx.CTX_CANCEL_DT > '2025-01-01'::date
   AND EXISTS (
         SELECT 1
         FROM daybreak.address ads
         WHERE ads.ADR_CUS_ID = car.CAR_CUS_ID
           AND ads.ADR_STATE_CD = 'NH'
           AND ads.ADR_CURRENT_IND = 'Y'
     ) -->
  AND NOT EXISTS (
        SELECT 1
        FROM simcoe_warranty.ancillary_cancellation_letters_history acl
        WHERE acl.acl_product = ctx.CTX_PRODUCT
          AND acl.acl_acc_nbr = ctx.ctx_acc_nbr
    )
    </select>

    <select id="getProvidersByContractId" resultType="com.westlakefinancial.technology.partner.batch.domain.ProviderInfo">
        SELECT COALESCE(wp.company_name, wp.administrator) AS provider,
               COALESCE(wp.company_email, wp.poc1_email) AS email,
               con_provider_id providerId,
               CON_PTC_COMPANY as company,
               COMPANY_NAME AS companyName,
               COMPANY_ADDRESS AS companyAddress,
               COMPANY_CITY AS companyCity,
               COMPANY_STATE_CD AS companyStateCd,
               COMPANY_ZIP AS companyZip,
               con.con_dealer_id AS dealerId,
               TRIM(
                       CASE
                           WHEN COMPANY_CITY IS NOT NULL THEN
                               COMPANY_CITY || ', ' || COMPANY_STATE_CD || ' ' || COMPANY_ZIP
                           ELSE
                               COMPANY_STATE_CD || ' ' || COMPANY_ZIP
                           END
                   ) AS cityStateZip
        FROM simcoe_warranty.contracts con
                 JOIN simcoe_warranty.warr_providers wp
                      ON con.con_provider_id = wp.company_id
        WHERE con.con_contract_id = #{contractId,jdbcType=VARCHAR}::integer
    </select>
</mapper>

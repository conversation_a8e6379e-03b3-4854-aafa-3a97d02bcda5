package com.westlakefinancial.technology.partner.batch.job;

import com.westlakefinancial.technology.partner.batch.domain.VerificationInfo;
import com.westlakefinancial.technology.partner.batch.listener.JobExitCodeListener;
import com.westlakefinancial.technology.partner.batch.listener.JobStepExecutionListener;
import com.westlakefinancial.technology.partner.batch.reader.VerificationWFSInfoReader;
import com.westlakefinancial.technology.partner.batch.tasklet.SendWFSEmailToProviderTasklets;
import com.westlakefinancial.technology.partner.batch.writer.VerificationInfoWriter;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.job.builder.FlowBuilder;
import org.springframework.batch.core.job.flow.Flow;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * @auther bf
 * @date 2024/10/29
 */
@Configuration
public class VerificationSendWFSEmailToProviderJob {
    @Resource
    private JobBuilderFactory jobBuilderFactory;

    @Resource
    private JobExitCodeListener jobExitCodeListener;

    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Resource
    private JobStepExecutionListener jobStepExecutionListener;

    @Resource
    private VerificationWFSInfoReader verificationWFSInfoReader;

    @Resource
    private VerificationInfoWriter verificationInfoWriter;

    @Resource
    private SendWFSEmailToProviderTasklets sendWFSEmailToProviderTasklets;

    @Bean
    public Job verificationWFSJob() {
        return jobBuilderFactory.get("verificationSendWFSEmailToProviderJob")
                .incrementer(new TimestampIncrementer())
                .start(sendVerificationWFSFlow())
                .end()
                .listener(jobExitCodeListener)
                .build();
    }

    @Bean
    public Step getVerificationWFSInfoStep() {
        return stepBuilderFactory.get("getVerificationWFSInfoStep")
                .<VerificationInfo, VerificationInfo>chunk(100)
                .reader(verificationWFSInfoReader)
                .writer(verificationInfoWriter)
                .listener(jobStepExecutionListener)
                .build();
    }

    @Bean
    public Flow sendVerificationWFSFlow() {
        return new FlowBuilder<Flow>("sendVerificationWFSFlow")
                .start(getVerificationWFSInfoStep())
                .next(sendWFSEmailToProviderStep())
                .end();
    }

    @Bean
    public Step sendWFSEmailToProviderStep() {
        return stepBuilderFactory.get("sendWFSEmailToProviderStep")
                .tasklet(sendWFSEmailToProviderTasklets)
                .listener(jobStepExecutionListener)
                .build();
    }

}

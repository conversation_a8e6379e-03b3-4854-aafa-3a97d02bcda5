package com.westlakefinancial.technology.partner.batch.reader;

import com.westlakefinancial.technology.partner.batch.domain.VerificationInfo;
import com.westlakefinancial.technology.partner.batch.mapper.war.WarDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.NonTransientResourceException;
import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;

/**
 * @auther bf
 * @date 2024/10/29
 */
@Component
@Slf4j
public class VerificationWFSInfoReader implements ItemReader<VerificationInfo> {

    private LinkedList<VerificationInfo> verificationDTOLinkedList;

    @Resource
    private WarDao warDao;
    @Override
    public VerificationInfo read() throws UnexpectedInputException, ParseException, NonTransientResourceException {
        if (null == verificationDTOLinkedList) {
            log.info("VerificationInfoReader start...");
            verificationDTOLinkedList = new LinkedList<>();
            verificationDTOLinkedList.addAll(warDao.getVerificationInfo("C-WF"));
        }
        if (CollectionUtils.isEmpty(verificationDTOLinkedList)) {
            return null;
        }
        return verificationDTOLinkedList.pop();
    }
}

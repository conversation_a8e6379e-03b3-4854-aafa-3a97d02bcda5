datasource:
    oracleWar:
        driverClassName: oracle.jdbc.driver.OracleDriver
        url: *******************************************
        username: war
        password: war

    oracleDaybreakLivdb:
        driverClassName: oracle.jdbc.driver.OracleDriver
        url: **********************************************************************
        username: daybreak
        password: daybreak

    alloy:
        read:
            driverClassName: org.postgresql.Driver
            url: ****************************************************************
            username: simcoe_warranty_app
            password: q4Mod=yUt=O&O_iSTaP3
        write:
            driverClassName: org.postgresql.Driver
            url: ***********************************************************
            username: simcoe_warranty_app
            password: q4Mod=yUt=O&O_iSTaP3
file:
    path: "D:\\document\\simcoe\\warranty\\autocheck-mileage"
    credentialsStream: "D:\\Temp\\gcs-simcoe-stage.json"
    projectId: "services-stage-310218"
    bucketName: "services-stage-310218-simcoe"
    bucketFolder: "simcoe/warranty/warranty_verification/"
    contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"


simcoe-service-command:
    host: https://service-gateway-int-s.westlakefinancial.com/simcoe-service-command/
#    host: http://localhost:9013/

oauth2:
    username: "account"
    password: "westlake!"
    tokenUrl: "https://service-gateway-int-s.westlakefinancial.com/oauth2-service/uas/oauth/token?grant_type=client_credentials"
    account-service: "https://service-gateway-int-s.westlakefinancial.com/account-service"
    payoff-service: "https://service-gateway-int-s.westlakefinancial.com/payoff-service"
    account-misc-service: "https://service-gateway-int-s.westlakefinancial.com/account-misc-service"
    dealer-service: "https://service-gateway-int-s.westlakefinancial.com/dealer-service"

testMode: N

templateFilePath: E:/Product/Other/Westlake_Direct_Title_Buyback_Automation/job/

mail:
    secureOneDealerCreditEmail: agent5@************
    secureOneDealerCreditEmailCC: agent1@************,agent5@************
    secureOneDealerCreditSubject: Simcoe Credit Batch Exception Report
    host: ************
    sender: agent1@************
    port: 25
    dailyContent: This is a simcoe credit batch exception report
    ancillary:
        error:
            from: agent1@************
            cc: agent2@************
            to: agent1@************
        from:
        cc:
        test:
            mode: Y
            email: agent1@************
    verification:
        wfs:
            from: agent1@************
            cc:
        wfi:
            from: agent1@************
            cc:
        resendDays: 7
        headers: "ACC_NBR,VIN,VEHICLE_YEAR,VEHICLE_MAKE,VEHICLE_MODEL,
        CUSTOMER_FIRST_NAME,CUSTOMER_LAST_NAME,ACTIVE_DATE,
        CONTRACT_DATE,DEALER,DEALER_CODE,DEALER_STATE,PROVIDER,
        WARRANTY_PREMIUM,ALL_DATA_CORRECT,ACTIVATED,NOTES"
        scanEmail: agent9@************
        retry:
            count: 4
        host: ************
        port: 25
        from: agent1@************
        test:
            mode: Y
            email: agent9@************
    scan:
        local-folder-path: D:\Temp\pdf2
testAccNbr: 60028059,30548668

mongodb:
    source-collection: testTriggeredFile

daybreak-common:
    url: https://service-gateway-int-s.westlakefinancial.com/daybreak-common-service

WPM-dealer:
    beforedays: 1


simcoe:
    filenetInternal:
        url:

package com.westlakefinancial.technology.partner.batch.listener;

import com.westlakefinancial.technology.partner.batch.domain.BatchCommonDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class JobExitCodeListener implements JobExecutionListener {

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private BatchCommonDto batchCommonDto;

    @Override
    public void beforeJob(JobExecution jobExecution) {
        log.info(jobExecution.getJobInstance().getJobName() + " start");
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        log.info(jobExecution.getJobInstance().getJobName() + " execution completed. ExitStatus="
                + jobExecution.getExitStatus().getExitCode());

        jobRepository.update(jobExecution);

        if (jobExecution.getStatus() == BatchStatus.FAILED || CollectionUtils.isNotEmpty(batchCommonDto.getErrorInfos())) {
            System.exit(1);
        } else {
            System.exit(0);
        }
    }
}

package com.westlakefinancial.technology.partner.batch.tasklet;

import com.westlakefinancial.technology.partner.batch.domain.CancelTxnsDTO;
import com.westlakefinancial.technology.partner.batch.domain.common.SecureOneDealerCredit;
import com.westlakefinancial.technology.partner.batch.mapper.liv.DaybreakLivDao;
import com.westlakefinancial.technology.partner.batch.mapper.war.WarDao;
import com.westlakefinancial.technology.partner.batch.service.SendMailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.mail.EmailException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class SecureOneDealerCreditTasklets implements Tasklet {

    @Resource
    private DaybreakLivDao daybreakLivDao;

    @Resource
    private WarDao warDao;

    @Value("${mail.secureOneDealerCreditEmail}")
    private String secureOneDealerCreditEmail;


    @Value("${testAccNbr}")
    private List<String> testAccNbr;

    @Resource
    private SendMailService sendMailService;

    @Value("${mail.secureOneDealerCreditSubject}")
    private String secureOneDealerCreditSubject;

    @Value("${mail.dailyContent}")
    private String dailyContent;

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext){
        List<CancelTxnsDTO> cancelList = new ArrayList<>();
        List<CancelTxnsDTO> sercureOneList = new ArrayList<>();
        try {
            List<CancelTxnsDTO> cancelSecureOneList = warDao.getCancelSecureOne(testAccNbr);
            for (CancelTxnsDTO cancelTxnsDTO : cancelSecureOneList) {
                processSecureOneDealerCreditTransaction(cancelTxnsDTO, cancelList,sercureOneList);
            }
            if (CollectionUtils.isNotEmpty(cancelList)) {
                File file = generateFile(cancelList);
                sendEmail(file);
            }
            if (CollectionUtils.isNotEmpty(sercureOneList)) {
                warDao.insertWfsSecureOneDealerHistory(sercureOneList);
            }
        } catch (Exception e) {
            log.error("SecureOneDealerCreditTasklets has error: ", e);
        }
        return RepeatStatus.FINISHED;
    }

    private void processSecureOneDealerCreditTransaction(CancelTxnsDTO cancelTxnsDTO, List<CancelTxnsDTO> cancelList, List<CancelTxnsDTO> sercureOneList) {
        CancelTxnsDTO cancelTxns = new CancelTxnsDTO();
        try {
            cancelTxns.setAccNbr(cancelTxnsDTO.getAccNbr());
            cancelTxns.setProduct(cancelTxnsDTO.getProduct());
            cancelTxns.setCancelType(cancelTxnsDTO.getCancelType());
            cancelTxns.setCancelDate(cancelTxnsDTO.getCancelDate());
            cancelTxns.setDealerAmt(cancelTxnsDTO.getDealerAmt());
            cancelTxns.setPremiumAmt(cancelTxnsDTO.getPremiumAmt());
            cancelTxns.setUser(SecureOneDealerCredit.SIMCOE);
            cancelTxns.setApp(SecureOneDealerCredit.SIMCOE_CREDIT_BATCH);

            daybreakLivDao.callSecureOneDealerCredit(cancelTxns);
            if (cancelTxns.getErrInd() != null && cancelTxns.getErrInd() == -1) {
                cancelTxns.setErrMsg(cancelTxns.getErrMsg());
                cancelTxns.setResultInd(SecureOneDealerCredit.ERROR);
                cancelList.add(cancelTxns);
                sercureOneList.add(cancelTxns);
            } else if (cancelTxns.getErrInd() != null && cancelTxns.getErrInd() == 0) {
                cancelTxns.setResultInd(SecureOneDealerCredit.SUCCEED);
                sercureOneList.add(cancelTxns);
            }

            log.info("The WFS_SECUREONE_DEALER_CREDIT response is {} ",cancelTxns);
        } catch (Exception e) {
            log.error("WFS_SECUREONE_DEALER_CREDIT has failed", e);
            cancelTxns.setErrMsg(e.getMessage());
            cancelList.add(cancelTxns);
            cancelTxns.setResultInd(SecureOneDealerCredit.ERROR);
            sercureOneList.add(cancelTxns);
        }
    }

    private File generateFile(List<CancelTxnsDTO> cancelList) {
        Workbook workbook = new XSSFWorkbook();
        String fileName = SecureOneDealerCredit.FILE_NAME;
        File tempFile;
        Sheet sheet = workbook.createSheet(fileName);
        setColumnWidths(sheet);

        Row headerRow = sheet.createRow(0);
        CellStyle headerStyle = createHeaderStyle(workbook);
        String[] headers = SecureOneDealerCredit.HEADERS;
        createHeaderRow(headerRow, headers, headerStyle);

        CellStyle dataStyle = createDataStyle(workbook);
        int rowIndex = 1;
        for (CancelTxnsDTO cancelTxnsDTO : cancelList) {
            Row dataRow = sheet.createRow(rowIndex++);
            dataRow.setHeightInPoints(18);
            createDataRow(dataRow, cancelTxnsDTO, dataStyle);
        }

        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            workbook.write(byteArrayOutputStream);
            workbook.close();
            tempFile = File.createTempFile(SecureOneDealerCredit.FILE_NAME, SecureOneDealerCredit.XLSX);
            try (FileOutputStream fileOut = new FileOutputStream(tempFile)) {
                byteArrayOutputStream.writeTo(fileOut);
            }
        } catch (IOException e) {
            log.error("Generating exception report has error", e);
            return null;
        }
        return tempFile;
    }

    private void setColumnWidths(Sheet sheet) {
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 15 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 15 * 256);
        sheet.setColumnWidth(4, 15 * 256);
        sheet.setColumnWidth(5, 25 * 256);
        sheet.setColumnWidth(6, 25 * 256);
        sheet.setColumnWidth(7, 30 * 256);
        sheet.setColumnWidth(8, 60 * 256);
    }

    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontName(SecureOneDealerCredit.CALIBRI);
        headerStyle.setFont(headerFont);
        return headerStyle;
    }

    private void createHeaderRow(Row headerRow, String[] headers, CellStyle headerStyle) {
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle dataStyle = workbook.createCellStyle();
        Font dataFont = workbook.createFont();
        dataFont.setFontName(SecureOneDealerCredit.CALIBRI);
        dataStyle.setFont(dataFont);
        return dataStyle;
    }

    private void createDataRow(Row dataRow, CancelTxnsDTO dto, CellStyle dataStyle) {
        dataRow.createCell(0).setCellValue(dto.getAccNbr());
        dataRow.createCell(1).setCellValue(dto.getProduct());
        dataRow.createCell(2).setCellValue(dto.getCancelType());
        dataRow.createCell(3).setCellValue(dto.getCancelDate());
        dataRow.createCell(4).setCellValue(dto.getDealerAmt().doubleValue());
        dataRow.createCell(5).setCellValue(dto.getPremiumAmt().doubleValue());
        dataRow.createCell(6).setCellValue(dto.getApp());
        dataRow.createCell(7).setCellValue(dto.getUser());
        dataRow.createCell(8).setCellValue(dto.getErrMsg());

        for (int i = 0; i < 9; i++) {
            dataRow.getCell(i).setCellStyle(dataStyle);
        }
    }

    private void sendEmail(File file) throws EmailException {
        sendMailService.sendEmailByAttach(secureOneDealerCreditSubject, dailyContent, secureOneDealerCreditEmail, file);
    }

}

package com.westlakefinancial.technology.partner.batch.domain;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.text.MessageFormat;

/**
 * @auther bf
 * @date 2024/11/5
 */
@Data
@AllArgsConstructor
public class MongoErrorInfo {
    /**
     * The Email id.
     */
    private String emailId;
    /**
     * The Subject.
     */
    private String subject;
    /**
     * The From.
     */
    private String from;
    /**
     * The Date.
     */
    private String date;
    /**
     * The Error.
     */
    private String error;

    @Override
    public String toString() {
        return MessageFormat.format("Email Id:{0}, Subject:{1}, From:{2}, Date:{3}, Error:{4}", emailId, subject, from, date, error);
    }

}

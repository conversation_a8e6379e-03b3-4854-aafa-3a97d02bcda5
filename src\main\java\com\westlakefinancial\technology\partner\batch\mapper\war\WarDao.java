package com.westlakefinancial.technology.partner.batch.mapper.war;

import com.westlakefinancial.technology.partner.batch.domain.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Mapper
public interface WarDao {

    /**
     * Merge AutoCheckMileage
     *
     * @param autoCheckMileagesList
     */
    void insertAutoCheckMileage(@Param("autoCheckMileageList") List<? extends AutoCheckMileage> autoCheckMileagesList);

    /**
     * Call RefCtxCancelMile
     */
    void callRefCtxCancelMile();

    List<TitleBuyBackAutomationDTO> getTitleBuyBackAutomationList();

    DetailAccountDTO getDetailAccountList(@Param("accNbr") String accNbr);

    List<String> getEmailList(@Param("dealerCode") String dealerCode);

    String emailCC();

    List<CancelTxnsDTO> getCancelSecureOne(@Param("testAccNbr") List<String> accNbr);

    void insertWfsSecureOneDealerHistory(@Param("cancelTxnsDTOList") List<CancelTxnsDTO> cancelTxnsDTOList);

    void insertVerificationInfo(@Param("verificationInfoList") List<? extends VerificationInfo> verificationInfoList);

    void insertLetterData(CommonEmailInfo commonEmailInfo);

    void updateSentCount(VerificationInfo verificationInfo);

    void insertExceptionQueue(VerificationInfo verificationInfo);

    LinkedList<VerificationInfo> getVerificationInfo(@Param("company")String company);
    LinkedList<VerificationInfo> getSendEmailInfo(@Param("company")String company);
    LinkedList<VerificationInfo> getWeeklySendEmailInfo(@Param("company")String company,
                                                        @Param("resendDays")Integer resendDays);

    void insertVerifyResultsError(VerifyResultsError warVerificationErrorDTO);
    void insertVerifyResults(VerifyResults warVerificationErrorDTO);
    void insertExceptionQueueByVerifyResults(VerifyResults warVerificationErrorDTO);
    VerificationInfo getVerificationInfoByEmailInfo(String accNbr);

    void insertNotes(Map<String, String> notesMap);

    void updateWarVerificationEnabled(@Param("accNbr") String accNbr);

    void updateWarVerificationErrorEnabled(@Param("accNbr") String accNbr);

    void updateVerifResultsEnabled(@Param("accNbr") String accNbr);

    Integer getActivatedCount(@Param("aadId")String aadId);


    /**
     * The first execution inserts all account and producer relationships.
     * @param num
     */
    void callInitAccProRelation(@Param("num") int num);

    /**
     * The first execution inserts all producers.
     * @param num
     */
    void callInitProducers(@Param("num") int num);

    void callMergeAccProRelation(@Param("num") int num, @Param("beforeDays") int beforeDays);

    void callMergeProducers(@Param("num") int num, @Param("beforeDays") int beforeDays);

    void copyProducers();

    void updateSpecialPro();

    void updateFirstSendInd(@Param("backupList")List<VerificationInfo> backupList);

    Integer getAutoCheckMileageCount(@Param("accNbr") String accountNbr,@Param("product") String product);

    void addEventLogInfo(EventLog eventLog);


    void updateContracts();

    void updateContractProviderId(Map<String, Object> map);

    void updateProviderFlag(@Param("providerId") String providerId,
                            @Param("flag") String flag);
}


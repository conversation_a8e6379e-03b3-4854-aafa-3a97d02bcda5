package com.westlakefinancial.technology.partner.batch.constants;

/**
 * Common Constant.
 *
 * <AUTHOR>
 */
public class CommonConstant {

    private CommonConstant() {
    }

    public static final int CAN_USE_MILEAGE_INDEX = 7;

    public static final int PAGE_INDEX_FIRST = 0;

    public static final int ACC_NBR_INDEX = 0;

    public static final int CTX_PRODUCT_INDEX = 1;

    public static final int CTX_CANCEL_REASON_INDEX = 2;

    public static final int CTX_CANCEL_DT_INDEX = 3;

    public static final int LAST_ODO_READING_INDEX = 4;

    public static final int LAST_ODO_DATE_INDEX = 5;

    public static final String DATE_FORMAT = "yyyy/MM/dd";

    public static final String VERIFICATION_SUBJECT = "VSC Verification - {0}";

    public static final String VERIFICATION_CONTENT =
            "<!DOCTYPE html>\n" +
            "<html lang=\"en\">\n" +
            "<head>\n" +
            "    <meta charset=\"UTF-8\">\n" +
            "    <title>Email Template</title>\n" +
            "</head>\n" +
            "<body>\n" +
            "    <p>Hello,</p>\n" +
            "    <p>Please review the attached spread which reflects funded VSCs that are pending verification of activation. Please confirm that the information provided is accurate and the VSC(s) have been activated for {0}. If you require further assistance, feel free to respond to this email or contact us at 866-619-2524.</p>\n" +
            "    <p>Thank you in advance for your time,</p>\n" +
            "    <p>Insurance Dept.<br>\n" +
            "    {0}</p>\n" +
            "</body>\n" +
            "</html>";

    public static final String ANCILLARY_PROVIDER_CONTENT =
            "Please check the attached letter.</br></br></br>"
                    + "<b>Do Not Reply To This Email</b> </br>"
                    + "Information emailed in response to this message will not become part of your file; submit your "
                    + "inquiries to contact us to <u><span style='color:blue'><EMAIL></span></u> "
                    + "or (888) 755-8455. This e-mail, including attachments, contains information that is confidential "
                    + "and may be legally privileged. This e-mail, including attachments, constitutes non-public "
                    + "information intended to be conveyed only to the designated recipient(s). If you are not an intended "
                    + "recipient, please delete this e-mail, including attachments, and notify me. The unauthorized use, "
                    + "dissemination, distribution or reproduction of this e-mail, including attachments, is prohibited"
                    + " and may be unlawful.\n";
}

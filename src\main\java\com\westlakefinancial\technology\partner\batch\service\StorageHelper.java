package com.westlakefinancial.technology.partner.batch.service;

import java.io.IOException;

/**
 * The interface Storage helper.
 *
 * <AUTHOR>
 */
public interface StorageHelper {
    /**
     * Upload.
     *
     * @param filePath the file path
     * @param destPath the dest path
     * @throws IOException the io exception
     */
    void upload(String filePath, String destPath) throws IOException;

    /**
     * Delete.
     *
     * @param destPath the dest path
     */
    void delete(String destPath);

    /**
     * Init.
     *
     * @throws IOException the io exception
     */
    void init() throws IOException;
}

package com.westlakefinancial.technology.partner.batch.service;

import com.westlakefinancial.technology.partner.batch.domain.CommonEmailInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.mail.EmailException;
import org.apache.commons.mail.HtmlEmail;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * Created by bf.
 */
@Service(value = "sendCommonMailService")
@Slf4j
public class CommonSendMailService {
    public void send(CommonEmailInfo commonEmailInfo) throws EmailException {
        log.info("Mail Information: host: {}, port: {}, To: {}, From: {}.",
                commonEmailInfo.getEmailHost(), commonEmailInfo.getEmailPort(), commonEmailInfo.getEmailTo(),
                commonEmailInfo.getEmailFrom());
        HtmlEmail email = new HtmlEmail();
        email.setHostName(commonEmailInfo.getEmailHost());
        email.setSmtpPort(Integer.parseInt(commonEmailInfo.getEmailPort()));
        email.isStartTLSEnabled();

        if (StringUtils.isNotEmpty(commonEmailInfo.getEmailTo())) {
            String[] emailRecipients = commonEmailInfo.getEmailTo().split(",");
            for (String recipient : emailRecipients) {
                email.addTo(recipient);
            }
        }
        if (StringUtils.isNotEmpty(commonEmailInfo.getEmailCc())) {
            String[] emailCC = commonEmailInfo.getEmailCc().split(",");
            for (String c : emailCC) {
                email.addCc(c);
            }
        }
        StringBuilder emailBody = new StringBuilder();
        emailBody.append(commonEmailInfo.getEmailContent());
        // attach files to email
        if (commonEmailInfo.getFile().exists()) {
            email.attach(commonEmailInfo.getFile());
        }
        // append sender
        email.setFrom(commonEmailInfo.getEmailFrom());
        // set Body and Subject
        email.setHtmlMsg(emailBody.toString());
        email.setSubject(commonEmailInfo.getSubject());
        email.send();
        if (commonEmailInfo.getFile().exists()) {
            commonEmailInfo.getFile().delete();
        }
    }

    public void send(CommonEmailInfo commonEmailInfo, List<File> fileList) throws EmailException {
        log.info("Mail Information: host: {}, port: {}, To: {}, From: {}.",
                commonEmailInfo.getEmailHost(), commonEmailInfo.getEmailPort(), commonEmailInfo.getEmailTo(),
                commonEmailInfo.getEmailFrom());
        HtmlEmail email = new HtmlEmail();
        email.setHostName(commonEmailInfo.getEmailHost());
        email.setSmtpPort(Integer.parseInt(commonEmailInfo.getEmailPort()));
        email.isStartTLSEnabled();

        if (StringUtils.isNotEmpty(commonEmailInfo.getEmailTo())) {
            String[] emailRecipients = commonEmailInfo.getEmailTo().split(",");
            for (String recipient : emailRecipients) {
                email.addTo(recipient);
            }
        }
        if (StringUtils.isNotEmpty(commonEmailInfo.getEmailCc())) {
            String[] emailCC = commonEmailInfo.getEmailCc().split(",");
            for (String c : emailCC) {
                email.addCc(c);
            }
        }
        StringBuilder emailBody = new StringBuilder();
        emailBody.append(commonEmailInfo.getEmailContent());
        // attach files to email
        for (File file : fileList) {
            email.attach(file);
        }
        // append sender
        email.setFrom(commonEmailInfo.getEmailFrom());
        // set Body and Subject
        email.setHtmlMsg(emailBody.toString());
        email.setSubject(commonEmailInfo.getSubject());
        email.send();

        for (File file : fileList) {
            if (file.exists()) {
                file.delete();
            }
        }
    }

}

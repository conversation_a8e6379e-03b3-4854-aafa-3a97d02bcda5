package com.westlakefinancial.technology.partner.batch.job;

import com.westlakefinancial.technology.partner.batch.domain.VerificationInfo;
import com.westlakefinancial.technology.partner.batch.listener.JobExitCodeListener;
import com.westlakefinancial.technology.partner.batch.listener.JobStepExecutionListener;
import com.westlakefinancial.technology.partner.batch.reader.VerificationWFIInfoReader;
import com.westlakefinancial.technology.partner.batch.tasklet.SendWFIEmailToProviderTasklets;
import com.westlakefinancial.technology.partner.batch.writer.VerificationInfoWriter;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.job.builder.FlowBuilder;
import org.springframework.batch.core.job.flow.Flow;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * @auther bf
 * @date 2025/01/13
 */
@Configuration
public class VerificationSendWFIEmailToProviderJob {
    @Resource
    private JobBuilderFactory jobBuilderFactory;

    @Resource
    private JobExitCodeListener jobExitCodeListener;

    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Resource
    private JobStepExecutionListener jobStepExecutionListener;

    @Resource
    private VerificationWFIInfoReader verificationWFIInfoReader;

    @Resource
    private VerificationInfoWriter verificationInfoWriter;

    @Resource
    private SendWFIEmailToProviderTasklets sendWFIEmailToProviderTasklets;

    @Bean
    public Job verificationWFIJob() {
        return jobBuilderFactory.get("verificationSendWFIEmailToProviderJob")
                .incrementer(new TimestampIncrementer())
                .start(sendVerificationWFIFlow())
                .end()
                .listener(jobExitCodeListener)
                .build();
    }

    @Bean
    public Step getVerificationWFIInfoStep() {
        return stepBuilderFactory.get("getVerificationWFIInfoStep")
                .<VerificationInfo, VerificationInfo>chunk(100)
                .reader(verificationWFIInfoReader)
                .writer(verificationInfoWriter)
                .listener(jobStepExecutionListener)
                .build();
    }

    @Bean
    public Flow sendVerificationWFIFlow() {
        return new FlowBuilder<Flow>("sendVerificationWFIFlow")
                .start(getVerificationWFIInfoStep())
                .next(sendWFIEmailToProviderStep())
                .end();
    }

    @Bean
    public Step sendWFIEmailToProviderStep() {
        return stepBuilderFactory.get("sendWFIEmailToProviderStep")
                .tasklet(sendWFIEmailToProviderTasklets)
                .listener(jobStepExecutionListener)
                .build();
    }
}

logging:
  config: classpath:logback.xml
spring:
  data:
    mongodb:
      uri: mongodb+srv://gts:<EMAIL>/filestorage-prod?retryWrites=true&w=majority
  application:
    name:
  kafka:
    properties:
      ssl.endpoint.identification.algorithm: https
      sasl.mechanism: PLAIN
      request.timeout.ms: 20000
      security.protocol: SASL_SSL
      retry.backoff.ms: 500
      sasl.jaas.config: org.apache.kafka.common.security.plain.PlainLoginModule required username='2LOYRBZNY27XNSPO' password='auQsvCxjyHV7rCOq8ObEZuBYpBhvirfbW66dnDWlSgaEF54mK184b1104xG5GNZR';
    producer:
      bootstrap-servers: pkc-lzvrd.us-west4.gcp.confluent.cloud:9092
      client-id: ${spring.application.name}
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    template:
      default-topic: simcoe-prod-service-policy
    kafkaInfo:
      topicArn: pkc-lzvrd.us-west4.gcp.confluent.cloud
      commandCallActivity: CALL_ACTIVITY
      commandComment: ADD_COMMENT

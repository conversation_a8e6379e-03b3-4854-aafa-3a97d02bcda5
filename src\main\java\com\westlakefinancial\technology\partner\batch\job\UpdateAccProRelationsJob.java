package com.westlakefinancial.technology.partner.batch.job;

import com.westlakefinancial.technology.partner.batch.listener.JobExitCodeListener;
import com.westlakefinancial.technology.partner.batch.listener.JobStepExecutionListener;
import com.westlakefinancial.technology.partner.batch.tasklet.MergeAccProRelationsTasklets;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.job.builder.FlowBuilder;
import org.springframework.batch.core.job.flow.Flow;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
public class UpdateAccProRelationsJob {

    @Resource
    private JobBuilderFactory jobBuilderFactory;

    @Resource
    private JobExitCodeListener jobExitCodeListener;

    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Resource
    private JobStepExecutionListener jobStepExecutionListener;

    @Resource
    private MergeAccProRelationsTasklets mergeAccProRelationsTasklets;

    @Resource
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Bean
    public Job mergeAccProRelationsProcessJob() {
        return jobBuilderFactory.get("updateAccProRelationsJob")
                .incrementer(new TimestampIncrementer())
                .start(mergeAccProRelationsFlow())
                .build()
                .listener(jobExitCodeListener)
                .build();
    }

    @Bean
    public Flow mergeAccProRelationsFlow() {
        return new FlowBuilder<Flow>("mergeAccProRelationsFlow")
                .split(taskExecutor)
                .add(
                        mergeAccProRelations0(),
                        mergeAccProRelations1(),
                        mergeAccProRelations2(),
                        mergeAccProRelations3(),
                        mergeAccProRelations4(),
                        mergeAccProRelations5(),
                        mergeAccProRelations6(),
                        mergeAccProRelations7())
                .build();
    }

    @Bean
    public Flow mergeAccProRelations0() {
        return new FlowBuilder<Flow>("mergeAccProRelations0").start(mergeAccProRelationsStep()).build();
    }

    @Bean
    public Flow mergeAccProRelations1() {
        return new FlowBuilder<Flow>("mergeAccProRelations1").start(mergeAccProRelationsStep()).build();
    }

    @Bean
    public Flow mergeAccProRelations2() {
        return new FlowBuilder<Flow>("mergeAccProRelations2").start(mergeAccProRelationsStep()).build();
    }

    @Bean
    public Flow mergeAccProRelations3() {
        return new FlowBuilder<Flow>("mergeAccProRelations3").start(mergeAccProRelationsStep()).build();
    }

    @Bean
    public Flow mergeAccProRelations4() {
        return new FlowBuilder<Flow>("mergeAccProRelations4").start(mergeAccProRelationsStep()).build();
    }

    @Bean
    public Flow mergeAccProRelations5() {
        return new FlowBuilder<Flow>("mergeAccProRelationsData5").start(mergeAccProRelationsStep()).build();
    }

    @Bean
    public Flow mergeAccProRelations6() {
        return new FlowBuilder<Flow>("mergeAccProRelationsData6").start(mergeAccProRelationsStep()).build();
    }

    @Bean
    public Flow mergeAccProRelations7() {
        return new FlowBuilder<Flow>("mergeAccProRelationsData7").start(mergeAccProRelationsStep()).build();
    }


    @Bean
    public Step mergeAccProRelationsStep() {
        return stepBuilderFactory.get("mergeAccProRelationsStep")
                .tasklet(mergeAccProRelationsTasklets)
                .listener(jobStepExecutionListener)
                .build();
    }

}

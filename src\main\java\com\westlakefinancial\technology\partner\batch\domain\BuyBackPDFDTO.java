package com.westlakefinancial.technology.partner.batch.domain;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class BuyBackPDFDTO {

    private String isRefile;

    private String agentId;

    private String aadId;

    private String companyType;

    private String dealerCode;

    private String buybackType;

    private String calculationType;

    private String netAmtDue;

    private String proratedFigure;

    private String manualDate;

    private String numPayments;

    private String dualBuybackType;

    private String agentEmail;

    private String faxNum;

    private byte[] pdfData;

    private String emailDealerBoolean;

    private List<String> emailList;

    private Map<String, Object> selectedPartner;

    private String manualDatePlus;

    private String accNbr;

    private  Map<String, String> detailAccount;

    private Map<String, String> buybackBreakdownMap;

    Map<String, String> userInfo;

    String emailFrom;

    private String titleAutoBatchFlag;

}

package com.westlakefinancial.technology.partner.batch.job;

import com.westlakefinancial.technology.partner.batch.listener.JobExitCodeListener;
import com.westlakefinancial.technology.partner.batch.listener.JobStepExecutionListener;
import com.westlakefinancial.technology.partner.batch.tasklet.MongoScanTasklet;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * @auther bf
 * @date 2024/10/29
 */
@Configuration
public class VerificationParseAttachmentsJob {
    @Resource
    private JobBuilderFactory jobBuilderFactory;

    @Resource
    private JobExitCodeListener jobExitCodeListener;

    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Resource
    private JobStepExecutionListener jobStepExecutionListener;

    @Resource
    private MongoScanTasklet mongoScanTasklet;

    @Bean
    public Job warVerificationJob() {
        return jobBuilderFactory.get("verificationParseAttachmentsJob")
                .incrementer(new TimestampIncrementer())
                .start(warrantyVerificationStep())
                .listener(jobExitCodeListener)
                .build();
    }

    @Bean
    public Step warrantyVerificationStep() {
        return stepBuilderFactory.get("warrantyVerificationStep")
                .tasklet(mongoScanTasklet)
                .listener(jobStepExecutionListener)
                .build();
    }

}

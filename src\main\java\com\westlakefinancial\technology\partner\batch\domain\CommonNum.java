package com.westlakefinancial.technology.partner.batch.domain;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class CommonNum {

  private int sharedVariable;

  @PostConstruct
  private void init() {
    sharedVariable = -1;
  }

  public synchronized int getSharedVariable() {
    this.sharedVariable++;
    return this.sharedVariable;
  }
}

package com.westlakefinancial.technology.partner.batch.utils;

import org.springframework.util.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * The type Mail utils.
 *
 * <AUTHOR>
 */
public class MailUtils {

    private MailUtils() {

    }

    /**
     * Gets email address.
     *
     * @param address the address
     * @return the email address
     */
    public static String extractEmailFromAddress(String address) {
        if (!StringUtils.hasText(address)) {
            return "";
        }

        Matcher matcher = Pattern.compile("<(.*?)>").matcher(address);

        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return address;
        }
    }

}

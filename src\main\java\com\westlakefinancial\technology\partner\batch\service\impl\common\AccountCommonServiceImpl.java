package com.westlakefinancial.technology.partner.batch.service.impl.common;

import com.westlakefinancial.technology.kafkalib.model.KafkaMessage;
import com.westlakefinancial.technology.kafkalib.services.ProduceMessages;
import com.westlakefinancial.technology.partner.batch.service.common.AccountCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
@Transactional
public class AccountCommonServiceImpl implements AccountCommonService {

    @Value("${spring.kafka.template.default-topic}")
    private String topic;

    @Value("${spring.kafka.kafkaInfo.topicArn}")
    private String topicArn;

    @Value("${spring.kafka.kafkaInfo.commandComment}")
    private String commandComment;

    @Value("${spring.kafka.kafkaInfo.commandCallActivity}")
    private String commandCallActivity;

    @Value("${spring.application.name}")
    private String applicationName;

    @Autowired
    private KafkaTemplate<String, KafkaMessage> kafkaTemplate;

    @Override
    public Map<String, String> sendCallActivity(Map<String, String> param) throws NoSuchAlgorithmException, IllegalAccessException {
        ProduceMessages producer = new ProduceMessages(topic);
        if (null != param && !param.isEmpty()) {
            if (null != param.get("aadId")) {
                String uuid;
                LinkedHashMap<String, String> kafkaData = new LinkedHashMap<>();
                kafkaData.put("catType", param.get("catType"));
                kafkaData.put("crtType", param.get("crtType"));
                kafkaData.put("bussType", "CALL_ACTIVITY");
                kafkaData.put("bussSubType", "CALL_ACTIVITY");
                kafkaData.put("appName", "SIMCOE");
                kafkaData.put("createdBy", param.get("userName"));
                kafkaData.put("accConditionCd", param.get("condition"));
                kafkaData.put("reasonCode", "");
                kafkaData.put("lastUpdatedBy", param.get("userName"));
                kafkaData.put("application", StringUtils.isNotEmpty(param.get("application")) ? param.get("application") : applicationName.toUpperCase());
                if (StringUtils.isNotEmpty(param.get("timeFlag"))) {
                    kafkaData.put("systemTime", String.valueOf(System.currentTimeMillis()));
                }

                String[] aadIdList = param.get("aadId").split(",");
                String[] accNbrList = param.get("accNbr").split(",");

                for (int i = 0; i < aadIdList.length; i++) {
                    kafkaData.put("aadId", aadIdList[i]);
                    kafkaData.put("accNbr", accNbrList[i]);
                    uuid = UUID.randomUUID().toString();
                    Map<String, String> desc = new HashMap<String, String>() {
                        {
                            put("call account activities", "call account activities");
                        }
                    };

                    KafkaMessage kafkaMessage = new KafkaMessage.KafkaMessageBuilder(commandCallActivity, uuid, topic,
                            topicArn, applicationName).setDescription(desc).setData(kafkaData).build();

                    producer.sendMessage(kafkaTemplate, kafkaMessage);
                }
            }
        }
        return param;
    }

    @Override
    public Map<String, String> sendComment(Map<String, String> param) throws NoSuchAlgorithmException, IllegalAccessException {

        ProduceMessages producer = new ProduceMessages(topic);
        if (null != param && !param.isEmpty()) {

            if (null != param.get("aadId")) {

                String[] aadIdList = param.get("aadId").split(",");
                String[] accNbrList = param.get("accNbr").split(",");

                String uuid;
                LinkedHashMap<String, String> daybreakMap = new LinkedHashMap<>();

                daybreakMap.put("acmComment", param.get("comment"));
                daybreakMap.put("acmCommentBy", param.get("userName"));
                daybreakMap.put("acmCommentTypeCd", param.get("commentTypeCd"));
                daybreakMap.put("acmCommentSubTypeCd", param.get("commentSubTypeCd"));
                daybreakMap.put("acmCommentImpInd", param.get("acmCommentImpInd"));
                daybreakMap.put("acmAadOrigSysXref", "UNDEFINED");
                daybreakMap.put("acmOrigSysXref", "UNDEFINED");
                daybreakMap.put("createdBy", param.get("userName"));
                daybreakMap.put("lastUpdatedBy", param.get("userName"));
                daybreakMap.put("bussType", "CALL_ACTIVITY");
                daybreakMap.put("bussSubType", "CALL_ACTIVITY");
                daybreakMap.put("appName", "SIMCOE");
                daybreakMap.put("systemTime", String.valueOf(System.currentTimeMillis()));

                for (int i = 0; i < aadIdList.length; i++) {
                    daybreakMap.put("acmAadId", aadIdList[i]);
                    daybreakMap.put("accNbr", accNbrList[i]);
                    uuid = UUID.randomUUID().toString();
                    Map<String, String> desc = new HashMap<String, String>();
                    desc.put("simcoe comment", "send comment to daybreak");
                    KafkaMessage kafkaMessage = new KafkaMessage.KafkaMessageBuilder(commandComment, uuid, topic,
                            topicArn, applicationName).setDescription(desc).setData(daybreakMap).build();
                    log.info(String.format("-----> Producing message START：[account call activity] --> %s",
                            kafkaMessage.toString()));
                    producer.sendMessage(kafkaTemplate, kafkaMessage);
                    log.info("-----> Producing message END：[account call activity] -->");
                }
            }
        }
        return param;
    }

}

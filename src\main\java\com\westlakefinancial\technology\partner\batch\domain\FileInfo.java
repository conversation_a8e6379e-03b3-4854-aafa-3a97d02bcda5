package com.westlakefinancial.technology.partner.batch.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

/**
 * The type File info.
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@ToString(exclude = {"extractedInfo"})
public class FileInfo {
    /**
     * The Id.
     */
    private String id;
    /**
     * The Path.
     */
    private String path;
    /**
     * The Name.
     */
    private String name;
    /**
     * The Original name.
     */
    private String originalName;
    /**
     * The Storage path.
     */
    private String storagePath;
    /**
     * The Extracted info.
     */
    private Map<String, String> extractedInfo;

    /**
     * Instantiates a new File info.
     *
     * @param id           the id
     * @param path         the path
     * @param name         the name
     * @param originalName the original name
     */
    public FileInfo(String id, String path, String name, String originalName) {
        this.id = id;
        this.path = path;
        this.name = name;
        this.originalName = originalName;
    }
}

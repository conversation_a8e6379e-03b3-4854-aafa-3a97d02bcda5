package com.westlakefinancial.technology.partner.batch.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * The type Exception utils.
 *
 * <AUTHOR>
 */
@Slf4j
public class ExceptionUtils {

    private ExceptionUtils() {

    }

    /**
     * Gets simple description.
     *
     * @param error the error
     * @return the simple description
     */
    public static String getSimpleDescription(String error) {
        String firstLine = error;
        try {
            if (StringUtils.hasText(error)) {
                String[] lines = error.split("\\r?\\n");
                for (String line : lines) {
                    if (StringUtils.hasText(line.trim())) {
                        firstLine = line;
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("[->] Get simple error description failed:{}, Message:{}", e.getClass().getSimpleName(), e.getMessage());
        }
        return firstLine;
    }
}

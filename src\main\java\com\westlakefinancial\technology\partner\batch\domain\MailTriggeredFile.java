package com.westlakefinancial.technology.partner.batch.domain;

import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

/**
 * The type Mail triggered file.
 *
 * <AUTHOR>
@Data
public class MailTriggeredFile {
    /**
     * The Id.
     */
    private ObjectId id;
    /**
     * The Files.
     */
    private List<File> files;
    /**
     * The Request context.
     */
    private RequestContext requestContext;
    /**
     * The creation date.
     */
    private String createDate;
    /**
     * The Class.
     */
    private String _class;
    /**
     * The Is resolved.
     */
    private String isResolved;

    /**
     * The type File.
     */
    @Data
    @ToString
    public static class File {
        public static final String RTF = ".RTF";
        public static final String PDF = ".PDF";
        public static final String EXCEL = ".xlsx";
        /**
         * The Name.
         */
        private String name;
        /**
         * The Original filename.
         */
        private String originalFilename;
        /**
         * The File id.
         */
        private String fileId;

        public String getExt() {
            if (originalFilename.contains(".")) {
                return originalFilename.substring(originalFilename.indexOf("."));
            }
            return "";
        }

    }

    /**
     * The type Request context.
     */
    @Data
    public static class RequestContext {
        /**
         * The From.
         */
        private String from;
        /**
         * The Envelope.
         */
        private String envelope;
        /**
         * The Charsets.
         */
        private String charsets;
        /**
         * The Text.
         */
        private String text;
        /**
         * The Html.
         */
        private String html;
        /**
         * The Attachments.
         */
        private String attachments;
        /**
         * The Headers.
         */
        private String headers;
        /**
         * The Dkim.
         */
        private String dkim;
        /**
         * The Sender ip.
         */
        private String sender_ip;
        /**
         * The Spf.
         */
        private String SPF;
        /**
         * The Attachment info.
         */
        @Field("attachment-info")
        private String attachment_info;
        /**
         * The Content IDs.
         */
        @Field("content-ids")
        private String content_ids;
        /**
         * The Subject.
         */
        private String subject;
        /**
         * The To.
         */
        private String to;
    }
}

package com.westlakefinancial.technology.partner.batch.job;

import com.westlakefinancial.technology.partner.batch.listener.JobExitCodeListener;
import com.westlakefinancial.technology.partner.batch.listener.JobStepExecutionListener;
import com.westlakefinancial.technology.partner.batch.tasklet.AncillaryProductCancellationNoticeTasklets;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * @auther bf
 * @date 2025/2/10
 */
@Configuration
public class AncillaryProductCancellationNoticeJob {
    @Resource
    private JobBuilderFactory jobBuilderFactory;

    @Resource
    private JobExitCodeListener jobExitCodeListener;

    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Resource
    private JobStepExecutionListener jobStepExecutionListener;

    @Resource
    private AncillaryProductCancellationNoticeTasklets ancillaryProductCancellationNoticeTasklets;

    @Bean
    public Job ancillaryProductCancellationNoticeBatchJob() {
        return jobBuilderFactory.get("ancillaryProductCancellationNoticeJob")
                .incrementer(new TimestampIncrementer())
                .start(ancillaryProductCancellationNoticeStep())
                .listener(jobExitCodeListener)
                .build();
    }

    @Bean
    public Step ancillaryProductCancellationNoticeStep() {
        return stepBuilderFactory.get("ancillaryProductCancellationNoticeStep")
                .tasklet(ancillaryProductCancellationNoticeTasklets)
                .listener(jobStepExecutionListener)
                .build();
    }
}

package com.westlakefinancial.technology.partner.batch.job;

import com.westlakefinancial.technology.partner.batch.listener.JobExitCodeListener;
import com.westlakefinancial.technology.partner.batch.listener.JobStepExecutionListener;
import com.westlakefinancial.technology.partner.batch.tasklet.MergeProducersTasklets;
import com.westlakefinancial.technology.partner.batch.tasklet.UpdateContractsTasklets;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.job.builder.FlowBuilder;
import org.springframework.batch.core.job.flow.Flow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
public class UpdateProducersJob {

    @Resource
    private JobBuilderFactory jobBuilderFactory;

    @Resource
    private JobExitCodeListener jobExitCodeListener;

    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Resource
    private JobStepExecutionListener jobStepExecutionListener;

    @Resource
    private MergeProducersTasklets mergeProducersTasklets;

    @Resource
    private UpdateContractsTasklets updateContractsTasklets;

    @Resource
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Bean
    public Job mergeProducersProcessJob() {
        return jobBuilderFactory.get("updateProducersJob")
                .incrementer(new TimestampIncrementer())
                .start(mergeProducersFlow())
                .next(updateContractsFlow())
                .build()
                .listener(jobExitCodeListener)
                .build();
    }

    @Bean
    public Flow updateContractsFlow() {
        return new FlowBuilder<Flow>("updateContractsFlow").start(updateContractsStep()).build();
    }

    @Bean
    public Step updateContractsStep() {
        return stepBuilderFactory.get("updateContractsStep")
                .tasklet(updateContractsTasklets)
                .listener(jobStepExecutionListener)
                .build();
    }

    @Bean
    public Flow mergeProducersFlow() {
        return new FlowBuilder<Flow>("mergeProducersFlow")
                .split(taskExecutor)
                .add(
                        mergeProducers0(),
                        mergeProducers1(),
                        mergeProducers2(),
                        mergeProducers3(),
                        mergeProducers4(),
                        mergeProducers5(),
                        mergeProducers6(),
                        mergeProducers7())
                .build();
    }

    @Bean
    public Flow mergeProducers0() {
        return new FlowBuilder<Flow>("mergeProducers0").start(mergeProducersStep()).build();
    }

    @Bean
    public Flow mergeProducers1() {
        return new FlowBuilder<Flow>("mergeProducers1").start(mergeProducersStep()).build();
    }

    @Bean
    public Flow mergeProducers2() {
        return new FlowBuilder<Flow>("mergeProducers2").start(mergeProducersStep()).build();
    }

    @Bean
    public Flow mergeProducers3() {
        return new FlowBuilder<Flow>("mergeProducers3").start(mergeProducersStep()).build();
    }

    @Bean
    public Flow mergeProducers4() {
        return new FlowBuilder<Flow>("mergeProducers4").start(mergeProducersStep()).build();
    }

    @Bean
    public Flow mergeProducers5() {
        return new FlowBuilder<Flow>("mergeProducersData5").start(mergeProducersStep()).build();
    }

    @Bean
    public Flow mergeProducers6() {
        return new FlowBuilder<Flow>("mergeProducersData6").start(mergeProducersStep()).build();
    }

    @Bean
    public Flow mergeProducers7() {
        return new FlowBuilder<Flow>("mergeProducersData7").start(mergeProducersStep()).build();
    }


    @Bean
    public Step mergeProducersStep() {
        return stepBuilderFactory.get("mergeProducersStep")
                .tasklet(mergeProducersTasklets)
                .listener(jobStepExecutionListener)
                .build();
    }

}

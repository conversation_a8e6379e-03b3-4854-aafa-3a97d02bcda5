package com.westlakefinancial.technology.partner.batch.domain;

import lombok.Data;

import java.util.Date;

/**
 * The type Email scan info.
 *
 * <AUTHOR>
 */
@Data
public class FileUploadInfo {
    /**
     * The Id.
     */
    private int id;
//    /**
//     * The Acc nbr.
//     */
//    private String accNbr;
    /**
     * The Vin nbr.
     */
    private String vinNbr;
    /**
     * The File name.
     */
    private String fileName;
    /**
     * The File path.
     */
    private String filePath;
    /**
     * The Source.
     */
    private String source;
    /**
     * The Created by.
     */
    private String createdBy;
    /**
     * The Creation date.
     */
    private Date creationDate;
    /**
     * The Last updated by.
     */
    private String lastUpdatedBy;
    /**
     * The Last update date.
     */
    private Date lastUpdateDate;
    /**
     * The File id.
     */
    private String fileId;
    /**
     * The Email id.
     */
    private String emailId;
}

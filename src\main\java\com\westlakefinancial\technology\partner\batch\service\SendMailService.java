package com.westlakefinancial.technology.partner.batch.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.mail.EmailException;
import org.apache.commons.mail.HtmlEmail;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * Created by wxd.
 */
@Service(value = "sendMailService")
@Slf4j
public class SendMailService {

    @Value("${mail.host}")
    private String mailSmtpHost;

    @Value("${mail.port}")
    private String mailPort;

    @Value("${mail.sender}")
    private String mailFrom;

    @Value("${mail.secureOneDealerCreditEmailCC}")
    private String secureOneDealerCreditEmailCC;


    public void sendEmailByAttach(String subject, String mailContent, String mailTo, File file) throws EmailException {
        log.info("Mail Information: host: {}, port: {}, To: {}, From: {}, Subject: {}, Content: {}.",
                mailSmtpHost, mailPort, mailTo, mailFrom,
                subject, mailContent);
        HtmlEmail email = new HtmlEmail();
        email.setHostName(mailSmtpHost);
        email.setSmtpPort(Integer.parseInt(mailPort));
        email.isStartTLSEnabled();

		if (StringUtils.isNotEmpty(mailTo)) {
			String[] emailRecipients = mailTo.split(",");
			for (String recipient : emailRecipients) {
				email.addTo(recipient);
			}
		}
		if (StringUtils.isNotEmpty(secureOneDealerCreditEmailCC)) {
			String[] emailCC = secureOneDealerCreditEmailCC.split(",");
			for (String c : emailCC) {
				email.addCc(c);
			}
		}
        StringBuilder emailBody = new StringBuilder();
        emailBody.append(mailContent);
        // attach files to email
        if (file.exists()){
            email.attach(file);
        }
        // append sender
        email.setFrom(mailFrom);
        // set Body and Subject
        email.setHtmlMsg(emailBody.toString());
        email.setSubject(subject);
        email.send();
        if (file.exists()){
            file.delete();
        }
    }


}

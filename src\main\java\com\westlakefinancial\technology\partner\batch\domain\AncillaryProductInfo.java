package com.westlakefinancial.technology.partner.batch.domain;

import lombok.Data;

/**
 * @auther bf
 * @date 2025/2/11
 */
@Data
public class AncillaryProductInfo {
    private String accNbr;
    private String firstName;
    private String lastName;
    private String vin;
    private String eventDt;
    private String eventType;
    private String mileage;
    private String estimatedRefundDue;
    private String companyAddress;
    private String companyCity;
    private String companyStateCd;
    private String companyZip;
    private String companyName;
    private String company;
    private String address1;
    private String address2;
    private String contractId;
    private String product;
    private String referenceId;
    private String referenceName;
    private String sentType;
    private String emailTo;
    private String subject;
    private String attachFileNames;
    private String providerName;
    private String cityStateZip;
    private String date;
    private String city;
    private String zip;
    private String state;
    private String providerId;
    private String dealerCode;
}

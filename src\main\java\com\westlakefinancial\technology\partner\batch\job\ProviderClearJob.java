package com.westlakefinancial.technology.partner.batch.job;

import com.westlakefinancial.technology.partner.batch.listener.JobExitCodeListener;
import com.westlakefinancial.technology.partner.batch.listener.JobStepExecutionListener;
import com.westlakefinancial.technology.partner.batch.tasklet.ProviderClearTasklet;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * @auther bf
 * @date 2025/4/5
 */
@Configuration
public class ProviderClearJob {
    @Resource
    private JobBuilderFactory jobBuilderFactory;

    @Resource
    private JobExitCodeListener jobExitCodeListener;

    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Resource
    private JobStepExecutionListener jobStepExecutionListener;

    @Resource
    private ProviderClearTasklet providerClearTasklet;

    @Bean
    public Job providerClearBatchJob() {
        return jobBuilderFactory.get("providerClearJob")
                .incrementer(new TimestampIncrementer())
                .start(providerClearStep())
                .listener(jobExitCodeListener)
                .build();
    }

    @Bean
    public Step providerClearStep() {
        return stepBuilderFactory.get("providerClearStep")
                .tasklet(providerClearTasklet)
                .listener(jobStepExecutionListener)
                .build();
    }
}

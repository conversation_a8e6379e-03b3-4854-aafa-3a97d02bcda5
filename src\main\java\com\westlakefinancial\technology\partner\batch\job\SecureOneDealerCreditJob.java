package com.westlakefinancial.technology.partner.batch.job;

import com.westlakefinancial.technology.partner.batch.listener.JobExitCodeListener;
import com.westlakefinancial.technology.partner.batch.listener.JobStepExecutionListener;
import com.westlakefinancial.technology.partner.batch.tasklet.SecureOneDealerCreditTasklets;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
public class SecureOneDealerCreditJob {

    @Resource
    private JobBuilderFactory jobBuilderFactory;

    @Resource
    private JobExitCodeListener jobExitCodeListener;

    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Resource
    private JobStepExecutionListener jobStepExecutionListener;

    @Resource
    private SecureOneDealerCreditTasklets secureOneDealerCreditTasklets;

    @Bean
    public Job secureOneDealerCreditEmailJob() {
        return jobBuilderFactory.get("secureOneDealerCreditJob")
                .incrementer(new TimestampIncrementer())
                .start(secureOneDealerCreditStep())
                .listener(jobExitCodeListener)
                .build();
    }

    @Bean
    public Step secureOneDealerCreditStep() {
        return stepBuilderFactory.get("secureOneDealerCreditStep")
                .tasklet(secureOneDealerCreditTasklets)
                .listener(jobStepExecutionListener)
                .build();
    }

}

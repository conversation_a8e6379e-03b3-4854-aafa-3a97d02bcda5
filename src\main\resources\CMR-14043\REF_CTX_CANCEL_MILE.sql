CREATE PROCEDURE "SIMCOE"."REF_CTX_CANCEL_MILE" AS
    v_ct NUMBER := 0;
BEGIN
    FOR rec IN (
        SELECT
            ctx_contract_id,
            am_acc_nbr,
            last_odo_mileage
        FROM
            autocheck_mileage   am
            JOIN cancel_txns         ctx
            ON am.am_acc_nbr = ctx.ctx_acc_nbr
            and am.CTX_PRODUCT = ctx.CTX_PRODUCT

    ) LOOP
        UPDATE cancel_txns
        SET
            ctx_cancel_mile = rec.last_odo_mileage
        WHERE
            ctx_contract_id = rec.ctx_contract_id;

        simcoe_collections.recalculate_cancel(rec.ctx_contract_id);
        v_ct := v_ct + 1;
        IF MOD(v_ct, 50) = 0 THEN
            COMMIT;
        END IF;

    END LOOP;

    COMMIT;
END ref_ctx_cancel_mile;

package com.westlakefinancial.technology.partner.batch.domain;

import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Component
public class BatchCommonDto {
    private List<ErrorInfo> errorInfos = new ArrayList<>();

    private List<Map<String, Object>> stepInfoList = new ArrayList<>();

    private Map<String, Object> jobInfo = new HashMap<>();


}

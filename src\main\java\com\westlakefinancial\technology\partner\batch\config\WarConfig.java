package com.westlakefinancial.technology.partner.batch.config;

import com.westlakefinancial.technology.partner.batch.exception.BusinessException;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {"com.westlakefinancial.technology.partner.batch.mapper.war"},
        sqlSessionFactoryRef = "warSqlSessionFactory")
@PropertySource(value = "classpath:commonConfig.yml", encoding = "utf-8", factory = YamlPropertySourceFactory.class)
public class WarConfig {
    @Value("${datasource.oracleWar.url}")
    private String consumerUrl;

    @Value("${datasource.oracleWar.username}")
    private String consumerUserName;

    @Value("${datasource.oracleWar.password}")
    private String consumerPassword;

    @Value("${datasource.oracleWar.driverClassName}")
    private String consumerDriver;

    @Bean(name = "warDataSource")
    @Primary
    public DataSource getDateSource() {
        return DataSourceBuilder.create().driverClassName(consumerDriver)
                .username(consumerUserName)
                .password(consumerPassword)
                .url(consumerUrl)
                .build();
    }
    /**
     * generate assignmentSqlSessionFactory.
     *
     * @return SqlSessionFactory
     */
    @Bean(name = "warSqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("warDataSource") DataSource datasource)
            throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(datasource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources("classpath*:com/westlakefinancial/technology/partner/batch/"
                        + "mapper/war/*.xml"));
        SqlSessionFactory sqlSessionFactory = bean.getObject();
        if(Objects.isNull(sqlSessionFactory)){
            throw new BusinessException("SqlSessionFactory is null");
        }
        sqlSessionFactory.getConfiguration().setJdbcTypeForNull(JdbcType.NULL);
        sqlSessionFactory.getConfiguration().setCallSettersOnNulls(true);
        return sqlSessionFactory;
    }

    @Bean(name = "warSqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(
            @Qualifier("warSqlSessionFactory") SqlSessionFactory sessionFactory) {
        return new SqlSessionTemplate(sessionFactory);
    }
}

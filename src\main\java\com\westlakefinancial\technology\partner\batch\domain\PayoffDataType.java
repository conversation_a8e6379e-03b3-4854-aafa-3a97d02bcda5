package com.westlakefinancial.technology.partner.batch.domain;

import lombok.Data;

@Data
public class PayoffDataType {
    private String accNbr;
    private String txnDt;
    private String txnPoqDt;
    private String txnPayoffFeeInd;
    private String txnPoqLtrPrintInd;
    private String txnComment;
    private String advancePrinciple;
    private String interest;
    private String feeLateCharge;
    private String feeNsf ;
    private String feeExtension;
    private String feeConvenience;
    private String feePhonePay;
    private String expenseLegal;
    private String expenseRepo;
    private String expenseDMV;
    private String expenseDeferred;
    private String futureLateCharge;
    private String interestAccrued;
    private String payoff;
    private String interestPerDiem;
    private String rebate;
    private String rebateInterest;
}

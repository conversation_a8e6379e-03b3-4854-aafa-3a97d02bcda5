<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westlakefinancial.technology.partner.batch.mapper.war.WarDao">

    <insert id="insertWfsSecureOneDealerHistory">
        <foreach collection="cancelTxnsDTOList" item="item" open="begin" close=";end;" index="index" separator=";">
            insert into WFS_SECUREONE_CREDIT_HISTORY (
            WSC_ID,
            WSC_ACC_NBR,
            WSC_PRODUCT,
            CREATED_DATE,
            WSC_RESULT_IND
            )
            values
            (
            WFS_SECUREONE_CREDIT_SEQ.nextval,
            #{item.accNbr,jdbcType=VARCHAR},
            #{item.product,jdbcType=VARCHAR},
            sysdate,
            #{item.resultInd,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="callInitAccProRelation" statementType="CALLABLE">
        <![CDATA[
            CALL pro_pkg.init_acc_pro_relations((#{num, mode=IN, jdbcType=INTEGER}))
        ]]>
    </insert>
    <insert id="copyProducers">
        <![CDATA[
            CALL pro_pkg.copy_producers()
        ]]>
    </insert>

    <update id="callInitProducers">
        <![CDATA[
            CALL pro_pkg.load_wpm_producers((#{num, mode=IN, jdbcType=INTEGER}))
        ]]>
    </update>

    <update id="callMergeAccProRelation">
        <![CDATA[
            CALL pro_pkg.merge_acc_pro_relations(#{num, mode=IN, jdbcType=INTEGER},#{beforeDays, mode=IN, jdbcType=INTEGER})
        ]]>
    </update>

    <update id="callMergeProducers">
        <![CDATA[
            CALL pro_pkg.merge_wpm_producers(#{num, mode=IN, jdbcType=INTEGER},#{beforeDays, mode=IN, jdbcType=INTEGER})
        ]]>
    </update>

    <update id="insertAutoCheckMileage" parameterType="java.util.List">
        <foreach collection="autoCheckMileageList" index="index" item="item" open=" begin " close=" end; "
                 separator=" ">
            MERGE INTO AUTOCHECK_MILEAGE am
            USING (
            SELECT DISTINCT
            ctx_acc_nbr AS ACC_NBR,
            CTX_PRODUCT AS PRODUCT
            FROM
            cancel_txns
            WHERE ctx_acc_nbr = #{item.amAccNbr,jdbcType=VARCHAR}
            AND CTX_PRODUCT = #{item.ctxProduct,jdbcType=VARCHAR}
            ) src
            ON (am.AM_ACC_NBR = src.ACC_NBR AND am.CTX_PRODUCT = src.PRODUCT)
            WHEN MATCHED THEN
            UPDATE
            SET
            CTX_CANCEL_REASON = #{item.ctxCancelReason,jdbcType=VARCHAR},
            CTX_CANCEL_DT = to_date(#{item.ctxCancelDt,jdbcType=DATE},'yyyy/MM/dd'),
            LAST_ODO_MILEAGE = #{item.lastOdoMileage,jdbcType=NUMERIC},
            LAST_ODO_DATE = to_date(#{item.lastOdoDate,jdbcType=DATE},'yyyy/MM/dd'),
            LAST_UPDATED_BY = 'autocheck-mileage-batch',
            LAST_UPDATE_DATE = sysdate
            WHERE AM_ACC_NBR = #{item.amAccNbr,jdbcType=VARCHAR}
            AND CTX_PRODUCT = #{item.ctxProduct,jdbcType=VARCHAR}
            WHEN NOT MATCHED THEN
            INSERT (
            AM_ID,
            AM_ACC_NBR,
            CTX_PRODUCT,
            CTX_CANCEL_REASON,
            CTX_CANCEL_DT,
            LAST_ODO_MILEAGE,
            LAST_ODO_DATE,
            CREATED_BY,
            CREATION_DATE,
            LAST_UPDATED_BY,
            LAST_UPDATE_DATE
            )
            VALUES (
            AM_ID_SEQ.NEXTVAL,
            #{item.amAccNbr,jdbcType=VARCHAR},
            #{item.ctxProduct,jdbcType=VARCHAR},
            #{item.ctxCancelReason,jdbcType=VARCHAR},
            to_date(#{item.ctxCancelDt,jdbcType=DATE},'yyyy/MM/dd'),
            #{item.lastOdoMileage,jdbcType=NUMERIC},
            to_date(#{item.lastOdoDate,jdbcType=DATE},'yyyy/MM/dd'),
            'autocheck-mileage-batch',
            sysdate,
            'autocheck-mileage-batch',
            sysdate
            );
        </foreach>
    </update>

    <update id="callRefCtxCancelMile">
        { CALL ref_ctx_cancel_mile()}
    </update>
    <update id="updateSpecialPro">
        update producers set PRO_GROUP_CD = PRO_NBR where PRO_NBR in ('OPENROAD', 'NALN', 'ILENDING', 'GRAVITY')
    </update>

    <select id="getTitleBuyBackAutomationList"
            resultType="com.westlakefinancial.technology.partner.batch.domain.TitleBuyBackAutomationDTO">
        SELECT
            ACC_NBR as accNbr,
            ACC_AAD_ID as accAadId,
            PRO.PRO_ID as proId,
            PRO_NBR as dealerCode,
            PRO_NAME AS dealerName,
            ASE_IDENTIFICATION_NBR as vin,
            NVL(ASE_YEAR, '') || ' ' || NVL(ASE_MAKE, '') || ' ' || NVL(ASE_MODEL, '') AS vinName,
            ACC_PTC_COMPANY as company
        FROM
            ACCOUNTS ACC
                LEFT JOIN ASSETS ASE
                          ON ASE_AAD_ID = ACC_AAD_ID
                              AND ASE_PRIMARY_IND = 'Y'
                LEFT JOIN ACCOUNT_CONDITIONS ACO
                          ON ACO.ACO_AAD_ID = ACC.ACC_AAD_ID
                              AND ACO.ACO_ACC_CONDITION_CD IN ('DC_INPROC', 'DCU', 'DC_LG_FILED', 'TITLENOTREQ','REINVSOLD','DC_RES','UNWIND','PBD')
                              AND ACO.ACO_ENABLED_IND = 'Y'
                LEFT JOIN PRODUCERS PRO
                          ON ACC_PRO_ID = PRO.PRO_ID

        WHERE
                    TRUNC(SYSDATE) - TRUNC(ACC_ACTIVE_DT) >=
                    120
          AND ASE.ASE_ASSET_STATUS_CD IN ('NOT', 'UNDEFINED', 'VER')
          AND ACO.ACO_ID IS NULL
          AND ACC_STATUS_CD NOT  IN ('PAID', 'VOID')
          AND ACC_PTC_COMPANY = 'C-WF'
          AND PRO_NBR IN (select CONF_KEY_1 from war_config where CONF_TYPE = 'REFINANCE_TITLE_AUTO_BATCH' AND CONF_KEY = 'DEALER_CODE')
    </select>

    <select id="getDetailAccountList"
            resultType="com.westlakefinancial.technology.partner.batch.domain.DetailAccountDTO">
        select acc.acc_nbr as accNbr,
               NVL(cus_first_name, '') || ' ' || NVL(cus_middle_name, '') || ' ' || NVL(cus_last_name, '') AS customerName,
               to_char(acc_contract_Dt, 'MM/dd/yyyy') as contractDate
        from accounts acc

                 join cus_acc_relations on acc_aad_id = car_aad_id and car_enabled_ind = 'Y'
                 join customers on car_cus_id = cus_id and cus_enabled_ind = 'Y'
        where acc.acc_nbr =  #{accNbr,jdbcType=VARCHAR} and car_relation_type_cd = 'PRIM'
    </select>

    <select id="getEmailList" resultType="java.lang.String">
        SELECT CONF_VALUE  FROM WAR_CONFIG WHERE CONF_KEY = 'DEALER_CODE' and CONF_KEY_1 = #{dealerCode,jdbcType=VARCHAR}
    </select>
    <select id="emailCC" resultType="java.lang.String">
        SELECT CONF_VALUE  FROM WAR_CONFIG WHERE CONF_KEY = 'EMAIL_CC' and CONF_TYPE ='REFINANCE_TITLE_AUTO_BATCH'
    </select>

    <select id="getCancelSecureOne"
            resultType="com.westlakefinancial.technology.partner.batch.domain.CancelTxnsDTO">
        SELECT  ctx.CTX_ACC_NBR                          as accNbr,
                ctx.CTX_PRODUCT                          as product,
                ctx.CTX_CANCEL_TYPE                      as cancelType,
                to_char(ctx.CTX_CANCEL_DT, 'MM/dd/yyyy') as cancelDate,
                ctx.CTX_DEALER_REFUND                    as dealerAmt,
                ctx.CTX_PREMIUM_AMT                      as premiumAmt
        FROM cancel_txns ctx
        WHERE 1 = 1
        <if test="testAccNbr != null and testAccNbr.size() > 0">
            AND CTX_ACC_NBR IN
            <foreach item="accNbr" collection="testAccNbr" open="(" separator="," close=")">
                #{accNbr,jdbcType=VARCHAR}
            </foreach>
        </if>
        AND ctx.CREATION_DATE  <![CDATA[ < ]]> SYSDATE - 120
        AND ctx.ctx_cancel_reason NOT IN ('CUSTOMER_REQUEST', 'BUYBACK')
        AND ctx.CTX_DEALER_REFUND > 0
        AND ctx.CTX_STATUS NOT IN ('RESOLVED', 'RESOLVED_DEALER','INELIGIBLE_WFI', 'EXPIRED', 'INELIGIBLE_BUYBACK', 'INELIGIBLE_DDFILE')
        AND ctx.ctx_product IN ('SECURE ONE', 'SECURE ONE PPM', 'SECURE ONE TIREWHEEL')
        AND ctx.CTX_CANCEL_DT <![CDATA[ >= ]]> to_date('01/01/2023','MM/DD/YYYY')
        AND (ctx.CTX_ACC_NBR,ctx.CTX_PRODUCT) not in (SELECT WSC_ACC_NBR, WSC_PRODUCT FROM WFS_SECUREONE_CREDIT_HISTORY)
        AND NOT EXISTS (

        SELECT 1

        FROM daybreak.TXNS

        WHERE CTX_AAD_ID = TXN_AAD_ID

        AND TXN_PRIMARY_IND = 'Y'

        AND (

        CASE

        WHEN CTX_PRODUCT = 'SECURE ONE' AND TXN_REASON_CD IN ('SO_WARR_CRED', 'SO_WARR_CRED_REV') THEN 'Y'

        WHEN CTX_PRODUCT = 'SECURE ONE PPM' AND TXN_REASON_CD IN ('SO_PPM_CRED', 'SO_PPM_CRED_REV') THEN 'Y'

        WHEN CTX_PRODUCT = 'SECURE ONE TIREWHEEL' AND TXN_REASON_CD IN ('SO_TNW_CRED', 'SO_TNW_CRED_REV') THEN 'Y'

        ELSE 'N'

        END

        ) = 'Y'

        )
    </select>
    <insert id="insertVerificationInfo">
        <foreach collection="verificationInfoList" item="item" open="begin" close=";end;" index="index" separator=";">
            insert into WAR_VERIFICATION(
            WV_ID,
            WV_ACC_NBR,
            WV_AAD_ID,
            WV_VIN,
            WV_VEHICLE_YEAR,
            WV_VEHICLE_MAKE,
            WV_VEHICLE_MODEL,
            WV_CUS_FIRST_NAME,
            WV_CUS_LAST_NAME,
            WV_ACTIVE_DT,
            WV_CONTRACT_DT,
            WV_DEALER,
            WV_DEALER_EMAIL,
            WV_PROVIDER,
            WV_PROVIDER_EMAIL,
            WV_DEALER_CODE,
            WV_DEALER_STATE,
            WV_PREMIUM,
            WV_LAST_SENT_DT,
            IS_ENABLED,
            WV_COMPANY,
            CREATED_BY,
            CREATION_DATE,
            LAST_UPDATED_BY,
            LAST_UPDATE_DATE
            )
            values
            (
            WAR_VERIFICATION_ID_SEQ.NEXTVAL,
            #{item.accNbr,jdbcType=VARCHAR},
            #{item.aadId,jdbcType=VARCHAR},
            #{item.vin,jdbcType=VARCHAR},
            #{item.vehicleYear,jdbcType=VARCHAR},
            #{item.vehicleMake,jdbcType=VARCHAR},
            #{item.vehicleModel,jdbcType=VARCHAR},
            #{item.cusFirstName,jdbcType=VARCHAR},
            #{item.cusLastName,jdbcType=VARCHAR},
            to_date(#{item.activeDt,jdbcType=DATE},'MM/dd/yyyy'),
            to_date(#{item.contractDt,jdbcType=DATE},'MM/dd/yyyy'),
            #{item.dealer,jdbcType=VARCHAR},
            #{item.dealerEmail,jdbcType=VARCHAR},
            #{item.newProvider,jdbcType=VARCHAR},
            #{item.newProviderEmail,jdbcType=VARCHAR},
            #{item.dealerCode,jdbcType=VARCHAR},
            #{item.dealerState,jdbcType=VARCHAR},
            #{item.premium,jdbcType=NUMERIC},
            to_date(#{item.lastSentDt,jdbcType=DATE},'MM/dd/yyyy'),
            'Y',
            #{item.company,jdbcType=VARCHAR},
            'SIMCOE-WARRANTY-BATCH',
            sysdate,
            'SIMCOE-WARRANTY-BATCH',
            sysdate
            )
        </foreach>
    </insert>

    <insert id="insertLetterData" parameterType="com.westlakefinancial.technology.partner.batch.domain.CommonEmailInfo">
        INSERT INTO SIMCOE_LETTERS
        (LTR_ID,
        <if test="accNbr != null and accNbr != ''">
            LTR_ACC_NBR,
        </if>
        <if test="dealerCode != null and dealerCode != ''">
            LTR_PRO_NBR,
        </if>
        <if test="fileName != null and fileName != ''">
            LTR_DOC_NAME,
        </if>
            LTR_SEND_BY,
        <if test="emailTo != null and emailTo != ''">
            LTR_EMAIL_TO,
        </if>
        <if test="emailCc != null and emailCc != ''">
            LTR_EMAIL_CC,
        </if>
        <if test="product != null and product != ''">
             LTR_PRODUCT,
        </if>
        <if test="emailType != null and emailType != ''">
            LTR_EMAIL_TYPE,
        </if>
        <if test="cancelDt != null and cancelDt !=''">
            LTR_CANCEL_DATE,
        </if>
        LTR_UUID,
        LTR_SEND_DT,
        LTR_DEPARTMENT)
        VALUES      ( SIMCOE_LETTERS_LTR_ID_SEQ.NEXTVAL,
        <if test="accNbr != null and accNbr != ''">
            #{accNbr},
        </if>
        <if test="dealerCode != null and dealerCode != ''">
            #{dealerCode},
        </if>
        <if test="fileName != null and fileName != ''">
            #{fileName},
        </if>
            'SIMCOE-WARRANTY-BATCH',
        <if test="emailTo != null and emailTo != ''">
            #{emailTo},
        </if>
        <if test="emailCc != null and emailCc != ''">
            #{emailCc},
        </if>
        <if test="product != null and product != ''">
            #{product},
        </if>
        <if test="emailType != null and emailType != ''">
            #{emailType},
        </if>
        <if test="cancelDt != null and cancelDt !=''">
            to_date(#{cancelDt},'MM/DD/YYYY'),
        </if>
        #{uuid},
        sysdate,
        <choose>
            <when test="ltrDepartment != null and ltrDepartment != ''">
                #{ltrDepartment}
            </when>
            <otherwise>
                ''
            </otherwise>
        </choose>)
    </insert>
    <update id="updateSentCount">
        update WAR_VERIFICATION
        set WV_NUM_SENT     = WV_NUM_SENT + 1,
            WV_LAST_SENT_DT = sysdate,
            LAST_UPDATED_BY = 'SIMCOE-WARRANTY-BATCH',
            LAST_UPDATE_DATE = sysdate
        where WV_ID = #{id,jdbcType=VARCHAR}
    </update>
    <insert id="insertExceptionQueue">
        insert into WAR_VERIFICATION_ERROR (WVE_ID,
                                            WVE_ACC_NBR,
                                            WVE_AAD_ID,
                                            WVE_VIN,
                                            WVE_VEHICLE_YEAR,
                                            WVE_VEHICLE_MAKE,
                                            WVE_VEHICLE_MODEL,
                                            WVE_CUS_FIRST_NAME,
                                            WVE_CUS_LAST_NAME,
                                            WVE_ACTIVE_DT,
                                            WVE_CONTRACT_DT,
                                            WVE_DEALER,
                                            WVE_DEALER_EMAIL,
                                            WVE_PROVIDER,
                                            WVE_PROVIDER_EMAIL,
                                            WVE_DEALER_CODE,
                                            WVE_DEALER_STATE,
                                            WVE_PREMIUM,
                                            WVE_LAST_SENT_DT,
                                            WVE_NUM_SENT,
                                            WVE_IS_IGNORE,
                                            IS_ENABLED,
                                            ERROR_RESULT,
                                            WVE_DATA_TYPE,
                                            WVE_COMPANY,
                                            CREATED_BY,
                                            CREATION_DATE,
                                            LAST_UPDATED_BY,
                                            LAST_UPDATE_DATE)
        values (WAR_VERIFICATION_ERROR_ID_SEQ.NEXTVAL,
                #{accNbr,jdbcType=VARCHAR},
                #{aadId,jdbcType=VARCHAR},
                #{vin,jdbcType=VARCHAR},
                #{vehicleYear,jdbcType=VARCHAR},
                #{vehicleMake,jdbcType=VARCHAR},
                #{vehicleModel,jdbcType=VARCHAR},
                #{cusFirstName,jdbcType=VARCHAR},
                #{cusLastName,jdbcType=VARCHAR},
                to_date(#{activeDt,jdbcType=DATE}, 'MM/dd/yyyy'),
                to_date(#{contractDt,jdbcType=DATE}, 'MM/dd/yyyy'),
                #{dealer,jdbcType=VARCHAR},
                #{dealerEmail,jdbcType=VARCHAR},
                #{provider,jdbcType=VARCHAR},
                #{providerEmail,jdbcType=VARCHAR},
                #{dealerCode,jdbcType=VARCHAR},
                #{dealerState,jdbcType=VARCHAR},
                #{premium,jdbcType=NUMERIC},
                to_date(#{lastSentDt,jdbcType=DATE}, 'MM/dd/yyyy'),
                #{numSent,jdbcType=NUMERIC},
                'N',
                'Y',
                #{errorResult,jdbcType=VARCHAR},
                #{dataType,jdbcType=VARCHAR},
                #{company,jdbcType=VARCHAR},
                'SIMCOE-WARRANTY-BATCH',
                sysdate,
                'SIMCOE-WARRANTY-BATCH',
                sysdate)
    </insert>
    <select id="getVerificationInfo" resultType="com.westlakefinancial.technology.partner.batch.domain.VerificationInfo">
        SELECT a.acc_nbr                                   AS accNbr,
               a.acc_aad_id                                AS aadId,
               a.acc_ptc_company                           AS company,
               mv.ase_identification_nbr                   AS vin,
               mv.ase_year                                 AS vehicleYear,
               mv.ase_make                                 AS vehicleMake,
               mv.ase_model                                AS vehicleModel,
               mv.cust_first_name                          AS cusFirstName,
               mv.cust_last_name                           AS cusLastName,
               to_char(a.acc_active_dt, 'MM/dd/yyyy')      AS activeDt,
               to_char(a.acc_contract_dt, 'MM/dd/yyyy')    AS contractDt,
               p.pro_name                                  AS dealer,
               p.pro_email_address                         AS dealerEmail,
               p.pro_nbr                                   AS dealerCode,
               p.PRO_STATE_CD                              AS dealerState,
               coalesce(wp.company_name, wp.administrator) AS provider,
               coalesce(wp.company_email, wp.poc1_email)   AS providerEmail,
               wc.CONF_VALUE                               AS newProvider,
               wc.CONF_VALUE_1                             AS newProviderEmail,
               c.CON_PREMIUM_AMT                           as premium
        FROM daybreak.accounts a
                 JOIN daybreak.mv_accounts mv ON mv.acc_aad_id = a.acc_aad_id
                 JOIN daybreak.account_conditions pv ON pv.aco_aad_id = a.acc_aad_id
            AND pv.aco_enabled_ind = 'Y'
            AND pv.aco_acc_condition_cd = 'SC_PV'
                 LEFT JOIN daybreak.producers p ON p.pro_id = a.acc_pro_id
                 JOIN contracts c ON c.con_aad_id = a.acc_aad_id
            AND c.con_product = 'WARRANTY'
                 LEFT JOIN warr_providers wp ON wp.company_id = c.con_provider_id
                 LEFT JOIN war_config wc
                           ON trim(upper(coalesce(wp.company_name, wp.administrator))) = trim(upper(wc.CONF_KEY))
                               and wc.CONF_TYPE = 'PROVIDER_VERIF'
        WHERE
            <![CDATA[
            a.acc_ptc_company = #{company,jdbcType=VARCHAR}
          AND a.acc_status_cd <> 'PAID'
          AND p.pro_group_cd NOT IN (
                                     'ENTERPRISE',
                                     'CARMAX',
                                     'AUTO NATION',
                                     'AUTONATION',
                                     'GROUP 1'
            )
          AND a.acc_active_dt < current_date - 7
          AND a.acc_nbr NOT IN (select WV_ACC_NBR
                                from WAR_VERIFICATION
                                where IS_ENABLED = 'Y')
          and a.acc_nbr NOT IN (select distinct WVE_ACC_NBR
                                FROM WAR_VERIFICATION_ERROR
                                WHERE IS_ENABLED = 'Y')
        ]]>
    </select>
    <select id="getSendEmailInfo" resultType="com.westlakefinancial.technology.partner.batch.domain.VerificationInfo">
        select WV_ID                                  AS id,
               WV_ACC_NBR                             AS accNbr,
               WV_AAD_ID                              AS aadId,
               WV_VIN                                 AS vin,
               WV_VEHICLE_YEAR                        AS vehicleYear,
               WV_VEHICLE_MAKE                        AS vehicleMake,
               WV_VEHICLE_MODEL                       AS vehicleModel,
               WV_CUS_FIRST_NAME                      AS cusFirstName,
               WV_CUS_LAST_NAME                       AS cusLastName,
               to_char(WV_ACTIVE_DT, 'MM/dd/yyyy')    AS activeDt,
               to_char(WV_CONTRACT_DT, 'MM/dd/yyyy')  AS contractDt,
               WV_DEALER                              AS dealer,
               WV_DEALER_EMAIL                        AS dealerEmail,
               WV_PROVIDER                            AS provider,
               WV_PROVIDER_EMAIL                      AS providerEmail,
               WV_DEALER_CODE                         AS dealerCode,
               WV_DEALER_STATE                        AS dealerState,
               WV_PREMIUM                             AS premium,
               to_char(WV_LAST_SENT_DT, 'MM/dd/yyyy') AS lastSentDt,
               WV_NUM_SENT                            AS numSent,
               WV_COMPANY                             AS company
        from WAR_VERIFICATION wv
                 JOIN daybreak.account_conditions pv ON pv.aco_aad_id = wv.wv_aad_id
            AND pv.aco_enabled_ind = 'Y'
            AND pv.aco_acc_condition_cd = 'SC_PV'
            AND wv.wv_first_send_ind is null
        WHERE
            wv.IS_ENABLED = 'Y'
            and wv.WV_ACC_NBR NOT IN (select distinct WVE_ACC_NBR
                                      FROM WAR_VERIFICATION_ERROR
                                      WHERE IS_ENABLED = 'Y')
            and wv.WV_ACC_NBR NOT IN (select distinct WVR_ACC_NBR
                                      FROM WAR_VERIF_RESULTS
                                      where IS_ENABLED = 'Y')
            and WV_COMPANY = #{company,jdbcType=VARCHAR}
    </select>

    <select id="getWeeklySendEmailInfo" resultType="com.westlakefinancial.technology.partner.batch.domain.VerificationInfo">
        select WV_ID                                  AS id,
               WV_ACC_NBR                             AS accNbr,
               WV_AAD_ID                              AS aadId,
               WV_VIN                                 AS vin,
               WV_VEHICLE_YEAR                        AS vehicleYear,
               WV_VEHICLE_MAKE                        AS vehicleMake,
               WV_VEHICLE_MODEL                       AS vehicleModel,
               WV_CUS_FIRST_NAME                      AS cusFirstName,
               WV_CUS_LAST_NAME                       AS cusLastName,
               to_char(WV_ACTIVE_DT, 'MM/dd/yyyy')    AS activeDt,
               to_char(WV_CONTRACT_DT, 'MM/dd/yyyy')  AS contractDt,
               WV_DEALER                              AS dealer,
               WV_DEALER_EMAIL                        AS dealerEmail,
               WV_PROVIDER                            AS provider,
               WV_PROVIDER_EMAIL                      AS providerEmail,
               WV_DEALER_CODE                         AS dealerCode,
               WV_DEALER_STATE                        AS dealerState,
               WV_PREMIUM                             AS premium,
               to_char(WV_LAST_SENT_DT, 'MM/dd/yyyy') AS lastSentDt,
               WV_NUM_SENT                            AS numSent,
               WV_COMPANY                             AS company
        from WAR_VERIFICATION wv
                 JOIN daybreak.account_conditions pv ON pv.aco_aad_id = wv.wv_aad_id
            AND pv.aco_enabled_ind = 'Y'
            AND pv.aco_acc_condition_cd = 'SC_PV'
            AND wv.wv_first_send_ind = 'Y'
            AND TRUNC(wv.LAST_UPDATE_DATE) &lt;= TRUNC(SYSDATE) - #{resendDays,jdbcType=INTEGER}
        WHERE
            wv.IS_ENABLED = 'Y'
            and wv.WV_ACC_NBR NOT IN (select distinct WVE_ACC_NBR
                                      FROM WAR_VERIFICATION_ERROR
                                      WHERE IS_ENABLED = 'Y')
            and wv.WV_ACC_NBR NOT IN (select distinct WVR_ACC_NBR
                                      FROM WAR_VERIF_RESULTS
                                      where IS_ENABLED = 'Y')
            and WV_COMPANY = #{company,jdbcType=VARCHAR}
    </select>

    <select id="getVerificationInfoByEmailInfo" resultType="com.westlakefinancial.technology.partner.batch.domain.VerificationInfo">
        select WV_ID                                  AS id,
               WV_ACC_NBR                             AS accNbr,
               WV_AAD_ID                              AS aadId,
               WV_VIN                                 AS vin,
               WV_VEHICLE_YEAR                        AS vehicleYear,
               WV_VEHICLE_MAKE                        AS vehicleMake,
               WV_VEHICLE_MODEL                       AS vehicleModel,
               WV_CUS_FIRST_NAME                      AS cusFirstName,
               WV_CUS_LAST_NAME                       AS cusLastName,
               to_char(WV_ACTIVE_DT, 'MM/dd/yyyy')    AS activeDt,
               to_char(WV_CONTRACT_DT, 'MM/dd/yyyy')  AS contractDt,
               WV_DEALER                              AS dealer,
               WV_DEALER_EMAIL                        AS dealerEmail,
               WV_PROVIDER                            AS provider,
               WV_PROVIDER_EMAIL                      AS providerEmail,
               WV_DEALER_CODE                         AS dealerCode,
               WV_DEALER_STATE                        AS dealerState,
               WV_PREMIUM                             AS premium,
               to_char(WV_LAST_SENT_DT, 'MM/dd/yyyy') AS lastSentDt,
               WV_NUM_SENT                            AS numSent
        from WAR_VERIFICATION
        where WV_ACC_NBR = #{accNbr,jdbcType=VARCHAR}
          AND IS_ENABLED = 'Y'
          and rownum = 1
    </select>

    <insert id="insertVerifyResultsError" parameterType="com.westlakefinancial.technology.partner.batch.domain.VerifyResultsError">
        insert into WAR_VERIF_RESULTS_ERROR (
        WVRE_ID,
        WVRE_EMAIL_ID,
        WVRE_GOOGLE_PATH,
        WVRE_IS_IGNORE,
        WVRE_EMAIL_FROM,
        WVRE_EMAIL_SUBJECT,
        <if test="emailSendDate != null and emailSendDate != ''">
            WVRE_EMAIL_SEND_DATE,
        </if>
        WVRE_EMAIL_TO,
        WVRE_EMAIL_HTML,
        CREATED_BY,
        CREATION_DATE,
        LAST_UPDATED_BY,
        LAST_UPDATE_DATE)
        values (WAR_VERIF_RESULTS_ERROR_ID_SEQ.NEXTVAL,
        #{emailId,jdbcType=VARCHAR},
        #{googlePath,jdbcType=VARCHAR},
        'N',
        #{emailFrom,jdbcType=VARCHAR},
        #{emailSubject,jdbcType=VARCHAR},
        <if test="emailSendDate != null and emailSendDate != ''">
            to_date(#{emailSendDate,jdbcType=DATE}, 'MM/dd/yyyy HH24:MI:SS'),
        </if>
        #{emailTo,jdbcType=VARCHAR},
        #{emailHtml,jdbcType=CLOB},
        'SIMCOE-WARRANTY-BATCH',
        sysdate,
        'SIMCOE-WARRANTY-BATCH',
        sysdate)
    </insert>
    <insert id="insertVerifyResults">
        insert into WAR_VERIF_RESULTS (WVR_ID,
                                       WVR_ACC_NBR,
                                       WVR_VIN,
                                       WVR_VEHICLE_YEAR,
                                       WVR_VEHICLE_MAKE,
                                       WVR_VEHICLE_MODEL,
                                       WVR_CUS_FIRST_NAME,
                                       WVR_CUS_LAST_NAME,
                                       WVR_ACTIVE_DATE,
                                       WVR_CONTRACT_DATE,
                                       WVR_DEALER,
                                       WVR_DEALER_CODE,
                                       WVR_DEALER_STATE,
                                       WVR_PROVIDER,
                                       WVR_WARRANTY_PREMIUM,
                                       WVR_ALL_DATA_CORRECTION,
                                       WVR_ACTIVATED,
                                       WVR_NOTES,
                                       IS_ENABLED,
                                       CREATION_DATE,
                                       CREATED_BY,
                                       LAST_UPDATE_DATE,
                                       LAST_UPDATED_BY)
        values (WAR_VERIF_RESULTS_ID_SEQ.NEXTVAL,
                #{accNbr,jdbcType=VARCHAR},
                #{vin,jdbcType=VARCHAR},
                #{vehicleYear,jdbcType=VARCHAR},
                #{vehicleMake,jdbcType=VARCHAR},
                #{vehicleModel,jdbcType=VARCHAR},
                #{cusFirstName,jdbcType=VARCHAR},
                #{cusLastName,jdbcType=VARCHAR},
                to_date(#{activeDt,jdbcType=DATE}, 'MM/dd/yyyy'),
                to_date(#{contractDt,jdbcType=DATE}, 'MM/dd/yyyy'),
                #{dealer,jdbcType=VARCHAR},
                #{dealerCode,jdbcType=VARCHAR},
                #{dealerState,jdbcType=VARCHAR},
                #{provider,jdbcType=VARCHAR},
                #{premium,jdbcType=NUMERIC},
                #{allDataCorrect,jdbcType=VARCHAR},
                #{activated,jdbcType=VARCHAR},
                #{notes,jdbcType=VARCHAR},
                'Y',
                sysdate,
                'SIMCOE-WARRANTY-BATCH',
                sysdate,
                'SIMCOE-WARRANTY-BATCH')
    </insert>

    <insert id="insertExceptionQueueByVerifyResults">
        insert into WAR_VERIFICATION_ERROR (WVE_ID,
                                            WVE_EMAIL_ID,
                                            WVE_ACC_NBR,
                                            WVE_AAD_ID,
                                            WVE_VIN,
                                            WVE_VEHICLE_YEAR,
                                            WVE_VEHICLE_MAKE,
                                            WVE_VEHICLE_MODEL,
                                            WVE_CUS_FIRST_NAME,
                                            WVE_CUS_LAST_NAME,
                                            WVE_ACTIVE_DT,
                                            WVE_CONTRACT_DT,
                                            WVE_DEALER,
                                            WVE_PROVIDER,
                                            WVE_PROVIDER_EMAIL,
                                            WVE_DEALER_CODE,
                                            WVE_DEALER_STATE,
                                            WVE_DEALER_EMAIL,
                                            WVE_PREMIUM,
                                            WVE_LAST_SENT_DT,
                                            WVE_NUM_SENT,
                                            WVE_IS_IGNORE,
                                            IS_ENABLED,
                                            WVE_ALL_DATA_CORRECTION,
                                            WVE_IS_CALL_ACTIVITIES,
                                            ERROR_RESULT,
                                            WVE_DATA_TYPE,
                                            WVE_COMPANY,
                                            CREATED_BY,
                                            CREATION_DATE,
                                            LAST_UPDATED_BY,
                                            LAST_UPDATE_DATE)
        values (WAR_VERIFICATION_ERROR_ID_SEQ.NEXTVAL,
                #{emailId,jdbcType=VARCHAR},
                #{accNbr,jdbcType=VARCHAR},
                #{aadId,jdbcType=VARCHAR},
                #{vin,jdbcType=VARCHAR},
                #{vehicleYear,jdbcType=VARCHAR},
                #{vehicleMake,jdbcType=VARCHAR},
                #{vehicleModel,jdbcType=VARCHAR},
                #{cusFirstName,jdbcType=VARCHAR},
                #{cusLastName,jdbcType=VARCHAR},
                to_date(#{activeDt,jdbcType=DATE}, 'MM/dd/yyyy'),
                to_date(#{contractDt,jdbcType=DATE}, 'MM/dd/yyyy'),
                #{dealer,jdbcType=VARCHAR},
                #{provider,jdbcType=VARCHAR},
                #{providerEmail,jdbcType=VARCHAR},
                #{dealerCode,jdbcType=VARCHAR},
                #{dealerState,jdbcType=VARCHAR},
                #{dealerEmail,jdbcType=VARCHAR},
                #{premium,jdbcType=NUMERIC},
                to_date(#{lastSentDt,jdbcType=DATE}, 'MM/dd/yyyy'),
                #{numSent,jdbcType=NUMERIC},
                'N',
                'Y',
                #{allDataCorrect,jdbcType=VARCHAR},
                #{activated,jdbcType=VARCHAR},
                #{errorResult,jdbcType=VARCHAR},
                #{dataType,jdbcType=VARCHAR},
                #{company,jdbcType=VARCHAR},
                'SIMCOE-WARRANTY-BATCH',
                sysdate,
                'SIMCOE-WARRANTY-BATCH',
                sysdate)
    </insert>

    <insert id="insertNotes" parameterType="map">
            INSERT INTO notes (note, note_type_cd, ref_id, ref_nbr, created_by, department)
            values (
            #{note,jdbcType=CLOB},
            'ACC',
            #{accAadId,jdbcType=VARCHAR},
            #{accNbr,jdbcType=VARCHAR},
            'SIMCOE-WARRANTY-BATCH',
            'WARRANTY'
            )
    </insert>

    <update id="updateWarVerificationEnabled">
        update WAR_VERIFICATION
        set IS_ENABLED = 'N',
            LAST_UPDATE_DATE = sysdate,
            LAST_UPDATED_BY = 'SIMCOE-WARRANTY-BATCH'
        where WV_ACC_NBR = #{accNbr,jdbcType=VARCHAR}
          and IS_ENABLED = 'Y'
    </update>

    <update id="updateWarVerificationErrorEnabled">
        update WAR_VERIFICATION_ERROR
        set IS_ENABLED = 'N',
            LAST_UPDATE_DATE = sysdate,
            LAST_UPDATED_BY = 'SIMCOE-WARRANTY-BATCH'
        where WVE_ACC_NBR = #{accNbr,jdbcType=VARCHAR}
          and IS_ENABLED = 'Y'
    </update>

    <update id="updateVerifResultsEnabled">
        update WAR_VERIF_RESULTS
        set IS_ENABLED = 'N',
            LAST_UPDATE_DATE = sysdate,
            LAST_UPDATED_BY = 'SIMCOE-WARRANTY-BATCH'
        where WVR_ACC_NBR = #{accNbr,jdbcType=VARCHAR}
          and IS_ENABLED = 'Y'
    </update>
    <update id="updateContracts">
        UPDATE CONTRACTS
        SET
            CON_DEALER_ID      = CON_PKG.get_new_dealer_id(CON_AAD_ID),
            CON_STRATEGIC_DLR       = CON_PKG.get_is_strategic_dealer(CON_PKG.get_new_dealer_id(CON_AAD_ID)),
            CON_DEALER_NAME         = CON_PKG.get_dealer_name(CON_PKG.get_new_dealer_id(CON_AAD_ID)),
            CON_DEALER_REGION       = CON_PKG.get_dealer_region_by_nbr(CON_PKG.get_new_dealer_id(CON_AAD_ID)),
            CON_DEALER_STATUS       = CON_PKG.get_dealer_status(CON_PKG.get_new_dealer_id(CON_AAD_ID))
        where CON_DEALER_ID IS NULL
    </update>

    <update id="updateFirstSendInd">
        <foreach collection="backupList" item="item" index="index" open="begin" close=";end;" separator=";">
            UPDATE WAR_VERIFICATION
            SET WV_FIRST_SEND_IND = 'Y',
                LAST_UPDATE_DATE = sysdate,
                LAST_UPDATED_BY = 'SIMCOE-WARRANTY-BATCH'
            WHERE WV_ACC_NBR = #{item.accNbr,jdbcType=VARCHAR}
            AND WV_FIRST_SEND_IND IS NULL
        </foreach>
    </update>

    <select id="getActivatedCount" resultType="integer">
        select count(1)
        from daybreak.account_conditions
        where aco_aad_id = #{aadId,jdbcType=VARCHAR}
          AND aco_enabled_ind = 'Y'
          AND aco_acc_condition_cd = 'SC_PV_ACT'
    </select>
    <select id="getAutoCheckMileageCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM AUTOCHECK_MILEAGE
        WHERE AM_ACC_NBR = #{accNbr,jdbcType=VARCHAR}
          AND CTX_PRODUCT = #{product,jdbcType=VARCHAR}
    </select>
    <insert id="addEventLogInfo" parameterType="com.westlakefinancial.technology.partner.batch.domain.EventLog">
        INSERT INTO war_event_log (WEL_ID, WEL_MESSAGE, WEL_ACC_NBR, WEL_PRODUCT, WEL_DEALER_CODE, WEL_LOG_TYPE,
                                   CREATION_DATE, CREATED_BY, WEL_BUSS_NAME, WEL_PROVIDER_CODE)
        Values (WAR_EVENT_LOG_SEQ.NEXTVAL,
                #{message,jdbcType = VARCHAR},
                #{accNbr,jdbcType = VARCHAR},
                UPPER(#{product,jdbcType = VARCHAR}),
                #{dealerCode,jdbcType = VARCHAR},
                #{logType,jdbcType = VARCHAR},
                sysdate,
                #{createdBy,jdbcType = VARCHAR},
                #{bussName,jdbcType = VARCHAR},
                #{providerId,jdbcType = VARCHAR})

    </insert>

    <!-- updateContractProviderId --> 

    <update id="updateContractProviderId" parameterType="map">
        update CONTRACTS
        set CON_PROVIDER_ID = #{providerId,jdbcType=VARCHAR}
        where CON_CONTRACT_ID = #{contractId,jdbcType=VARCHAR}
    </update>

    <!-- updateProviderFlag --> 

    <update id="updateProviderFlag">
        update warr_providers
        set CORRECT_FLAG = #{flag,jdbcType = VARCHAR}
        where COMPANY_ID = #{providerId,jdbcType = VARCHAR}
    </update>    
    </mapper>

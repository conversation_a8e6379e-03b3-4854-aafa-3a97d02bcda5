package com.westlakefinancial.technology.partner.batch.job;

import com.westlakefinancial.technology.partner.batch.domain.AutoCheckMileage;
import com.westlakefinancial.technology.partner.batch.listener.JobExitCodeListener;
import com.westlakefinancial.technology.partner.batch.listener.JobStepExecutionListener;
import com.westlakefinancial.technology.partner.batch.reader.AutoCheckMileageReader;
import com.westlakefinancial.technology.partner.batch.tasklet.RefCtxCancelMileTasklets;
import com.westlakefinancial.technology.partner.batch.writer.AutoCheckMileageWriter;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
public class AutoCheckMileageJob {

    @Resource
    private JobBuilderFactory jobBuilderFactory;

    @Resource
    private JobExitCodeListener jobExitCodeListener;

    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Resource
    private JobStepExecutionListener jobStepExecutionListener;

    @Resource
    private AutoCheckMileageReader autoCheckMileageReader;

    @Resource
    private AutoCheckMileageWriter autoCheckMileageWriter;

    @Resource
    private RefCtxCancelMileTasklets refCtxCancelMileTasklets;

    @Bean
    public Job autoCheckMileageProcessJob() {
        return jobBuilderFactory.get("autoCheckMileageJob")
                .incrementer(new TimestampIncrementer())
                .start(autoCheckMileageStep())
                .next(refCtxCancelMileStep())
                .listener(jobExitCodeListener)
                .build();
    }

    @Bean
    public Step autoCheckMileageStep() {
        return stepBuilderFactory.get("autoCheckMileageStep")
                .<AutoCheckMileage, AutoCheckMileage>chunk(100)
                .reader(autoCheckMileageReader)
                .writer(autoCheckMileageWriter)
                .listener(jobStepExecutionListener)
                .build();
    }

    @Bean
    public Step refCtxCancelMileStep() {
        return stepBuilderFactory.get("refCtxCancelMileStep")
                .tasklet(refCtxCancelMileTasklets)
                .listener(jobStepExecutionListener)
                .build();
    }
}

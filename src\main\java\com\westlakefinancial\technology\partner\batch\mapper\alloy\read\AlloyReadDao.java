package com.westlakefinancial.technology.partner.batch.mapper.alloy.read;

import com.westlakefinancial.technology.partner.batch.domain.AncillaryProductInfo;
import com.westlakefinancial.technology.partner.batch.domain.ProviderInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface AlloyReadDao {

    List<AncillaryProductInfo> getAncillaryProductInfo();

    ProviderInfo getProvidersByContractId(@Param("contractId") String contractId);

}


package com.westlakefinancial.technology.partner.batch;

import com.westlakefinancial.technology.partner.batch.utils.Counter;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableBatchProcessing
public class Application {

    public static void main(String[] args) {
        Map<String, String> params = new HashMap<>();
        for (int i=0; i<args.length; i++) {
            String[] kv = args[i].split("=");
            params.put(kv[0], kv.length > 1 ? kv[1] : kv[0]);
        }
        Counter.saveRunParam(params);
        SpringApplication.run(Application.class, args);
    }

}

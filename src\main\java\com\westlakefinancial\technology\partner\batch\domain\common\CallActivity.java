package com.westlakefinancial.technology.partner.batch.domain.common;


import lombok.Data;

@Data
public class CallActivity {

	private long id; //CAC_ID
    private String actionCd; //CAC_ACT_CD
    private String resultCd; //CAC_RST_CD
    private String resultDesc; //for display in data table only

    private String objectLevel; //CAC_OBJECT_LEVEL, DLR or ACC
    private String proNbr; //CAC_PRO_NBR
    private String accNbr; //CAC_ACC_NBR
    private String vinNbr;
    private long userId; //CAC_USER_ID
    private String department; //CAC_DEPARTMENT
    private String comment; //CAC_COMMENT
    private String creationDate; //CAC_CREATION_DT

    private String isFollowup; //CAC_IS_FOLLOWUP, Y or N
    private String followupDate; //CAC_FOLLOWUP_DT
    private String followupClosedDate; //CAC_FOLLOWUP_CLOSED_DT
    private String promiseDate; //CAC_PROMISE_DATE
    private Double promiseAmount; //CAC_PROMISE_AMT
    private String promiseTakenDate; //CAC_PROMISE_TAKEN_DT
    private Double promiseCollectedAmount;  //CAC_PROMISE_COLLECTED_AMT
    private String promiseBroken; //CAC_PROMISE_BROKEN
    private String promiseClosedDate; //CAC_PROMISE_CLOSED_DT
    private String contact; //CAC_CONTACT
    private String userName; //same as userId, for display friendly.
    private String roleName;
    private String followUpType;
    private String cacProviderNbr;
    private String producerId;
    private long loginUserId;
    private String phone;
    private String email;


    private String followUpEnabled ;

    private Double contractFees;
    private Double vrFees;

    private String currentPage;
    private String aadId;
    private String userCompany;
    private String tccValue5;
    private String tccValue6;
}

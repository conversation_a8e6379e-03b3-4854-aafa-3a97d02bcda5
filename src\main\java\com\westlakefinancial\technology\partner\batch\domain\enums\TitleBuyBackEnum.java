package com.westlakefinancial.technology.partner.batch.domain.enums;

/**
 * <AUTHOR>
 * @date ：Created in 2024/2/20 17:25
 */
public enum TitleBuyBackEnum {

    DUALBUYBACKTYPE(1, "E"),
    ISREFILE(2, "N"),
    AL<PERSON>(3, "all"),
    RETITLE(4, "RETIT<PERSON>"),
    NERAMTDUE(5, "0"),
    CALCULATIONTYPE(6, "4");

    private Integer code;
    private String value;

    TitleBuyBackEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getValueByCode(Integer code) {
        if (code != null) {
            TitleBuyBackEnum[] enums = TitleBuyBackEnum.values();
            for (TitleBuyBackEnum item : enums) {
                if (item.code.equals(code)) {
                    return item.value;
                }
            }
        }
        return "";
    }

    public static Integer getCodeByValue(String v) {
        TitleBuyBackEnum[] enums = TitleBuyBackEnum.values();
        for (TitleBuyBackEnum item : enums) {
            if (item.value.equals(v)) {
                return item.code;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}

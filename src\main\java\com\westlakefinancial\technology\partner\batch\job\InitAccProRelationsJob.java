package com.westlakefinancial.technology.partner.batch.job;

import com.westlakefinancial.technology.partner.batch.listener.JobExitCodeListener;
import com.westlakefinancial.technology.partner.batch.listener.JobStepExecutionListener;
import com.westlakefinancial.technology.partner.batch.tasklet.InitAccProRelationsTasklets;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.job.builder.FlowBuilder;
import org.springframework.batch.core.job.flow.Flow;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
public class InitAccProRelationsJob {

    @Resource
    private JobBuilderFactory jobBuilderFactory;

    @Resource
    private JobExitCodeListener jobExitCodeListener;

    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Resource
    private JobStepExecutionListener jobStepExecutionListener;

    @Resource
    private InitAccProRelationsTasklets initAccProRelationsTasklets;

    @Resource
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Bean
    public Job initAccProRelationsProcessJob() {
        return jobBuilderFactory.get("initAccProRelationsJob")
                .incrementer(new TimestampIncrementer())
                .start(initAccProRelationsFlow())
                .build()
                .listener(jobExitCodeListener)
                .build();
    }

    @Bean
    public Flow initAccProRelationsFlow() {
        return new FlowBuilder<Flow>("initAccProRelationsFlow")
                .split(taskExecutor)
                .add(
                        initAccProRelations0(),
                        initAccProRelations1(),
                        initAccProRelations2(),
                        initAccProRelations3(),
                        initAccProRelations4(),
                        initAccProRelations5(),
                        initAccProRelations6(),
                        initAccProRelations7())
                .build();
    }

    @Bean
    public Flow initAccProRelations0() {
        return new FlowBuilder<Flow>("initAccProRelations0").start(initAccProRelationsStep()).build();
    }

    @Bean
    public Flow initAccProRelations1() {
        return new FlowBuilder<Flow>("initAccProRelations1").start(initAccProRelationsStep()).build();
    }

    @Bean
    public Flow initAccProRelations2() {
        return new FlowBuilder<Flow>("initAccProRelations2").start(initAccProRelationsStep()).build();
    }

    @Bean
    public Flow initAccProRelations3() {
        return new FlowBuilder<Flow>("initAccProRelations3").start(initAccProRelationsStep()).build();
    }

    @Bean
    public Flow initAccProRelations4() {
        return new FlowBuilder<Flow>("initAccProRelations4").start(initAccProRelationsStep()).build();
    }

    @Bean
    public Flow initAccProRelations5() {
        return new FlowBuilder<Flow>("initAccProRelations5").start(initAccProRelationsStep()).build();
    }

    @Bean
    public Flow initAccProRelations6() {
        return new FlowBuilder<Flow>("initAccProRelations6").start(initAccProRelationsStep()).build();
    }

    @Bean
    public Flow initAccProRelations7() {
        return new FlowBuilder<Flow>("initAccProRelations7").start(initAccProRelationsStep()).build();
    }


    @Bean
    public Step initAccProRelationsStep() {
        return stepBuilderFactory.get("initAccProRelationsStep")
                .tasklet(initAccProRelationsTasklets)
                .listener(jobStepExecutionListener)
                .build();
    }

}

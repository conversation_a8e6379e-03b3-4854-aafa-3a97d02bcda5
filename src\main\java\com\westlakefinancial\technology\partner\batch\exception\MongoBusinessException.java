package com.westlakefinancial.technology.partner.batch.exception;


import com.westlakefinancial.technology.partner.batch.utils.ExceptionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class MongoBusinessException extends RuntimeException {

    public static final String DELI = ":";
    public static final String JOINER = ",";
    private final String message;
    private final Map<String, Object> properties = new HashMap<>();

    public MongoBusinessException(Exception e, String message, Map<String, Object> properties) {
        super(e);
        this.message = message;
        if (properties != null && !properties.isEmpty()) {
            this.properties.putAll(properties);
        }
    }

    public MongoBusinessException(String message, Map<String, Object> properties) {
        this.message = message;
        if (properties != null && !properties.isEmpty()) {
            this.properties.putAll(properties);
        }
    }

    @Override
    public String getMessage() {
        String msg = this.message;
        if (this.getCause() != null) {
            msg += "\r\n Inner exception is " + this.getCause().getClass().getSimpleName() + ":" + ExceptionUtils.getSimpleDescription(this.getCause().getMessage());
        }
        if (!properties.isEmpty()) {
            msg += "\r\n Additional information:" + properties.entrySet().stream().map(t-> t.getKey() + DELI + t.getValue()).collect(Collectors.joining(JOINER));
        }
        return msg;
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westlakefinancial.technology.partner.batch.mapper.alloy.write.AlloyWriteDao">
    <select id="insertAncillaryCancellationLettersHistory">
        INSERT INTO simcoe_warranty.ancillary_cancellation_letters_history (acl_acc_nbr,
                                                            acl_product,
                                                            acl_sent_type,
                                                            acl_reference_id,
                                                            acl_reference_name,
                                                            acl_mileage,
                                                            acl_cancel_date,
                                                            acl_event_type,
                                                            acl_reference_address,
                                                            acl_reference_city,
                                                            acl_reference_zip,
                                                            acl_reference_state,
                                                            acl_cust_first_name,
                                                            acl_cust_last_name,
                                                            acl_cust_est_refund_amount,
                                                            acl_cust_address1,
                                                            acl_cust_address2,
                                                            acl_cust_state,
                                                            acl_cust_city,
                                                            acl_cust_zip,
                                                            acl_email_to,
                                                            acl_subject,
                                                            acl_attach_file_names,
                                                            acl_email_sent_dt,
                                                            acl_vin,
                                                            created_by,
                                                            creation_date,
                                                            last_updated_by,
                                                            last_update_date)
        VALUES (#{accNbr},
                #{product},
                #{sentType},
                #{providerId},
                #{providerName},
                #{mileage},
                #{eventDt},
                #{eventType},
                #{companyAddress},
                #{companyCity},
                #{companyZip},
                #{companyStateCd},
                #{firstName},
                #{lastName},
                #{estimatedRefundDue},
                #{address1},
                #{address2},
                #{state},
                #{city},
                #{zip},
                #{emailTo},
                #{subject},
                #{attachFileNames},
                NOW(),
                #{vin},
                #{createdBy},
                NOW(),
                #{lastUpdatedBy},
                NOW())
    </select>
</mapper>

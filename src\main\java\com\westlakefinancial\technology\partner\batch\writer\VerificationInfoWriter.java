package com.westlakefinancial.technology.partner.batch.writer;

import com.westlakefinancial.technology.partner.batch.domain.VerificationInfo;
import com.westlakefinancial.technology.partner.batch.mapper.war.WarDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @auther bf
 * @date 2024/10/29
 */
@Component
@Slf4j
public class VerificationInfoWriter implements ItemWriter<VerificationInfo> {
    @Resource
    private WarDao warDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void write(List<? extends VerificationInfo> verificationList) {
        log.info("VerificationInfoWriterWriter start...");
        List<VerificationInfo> modifiableVerificationList = new ArrayList<>(verificationList);

        //Filter out empty providers
        List<VerificationInfo> emptyProviderList = modifiableVerificationList.stream().filter(verification-> StringUtils.isEmpty(verification.getProvider())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(emptyProviderList)){
            for(VerificationInfo verificationInfo:emptyProviderList) {
                verificationInfo.setDataType("PROVIDER");
                verificationInfo.setErrorResult("Current account has no provider.");
                warDao.insertExceptionQueue(verificationInfo);
            }
            Set<VerificationInfo> emptyProviderSet = new HashSet<>(emptyProviderList);
            modifiableVerificationList.removeIf(emptyProviderSet::contains);
        }

        //Filter out empty new providers
        List<VerificationInfo> emptyNewProviderList = modifiableVerificationList.stream().filter(verification-> StringUtils.isEmpty(verification.getNewProvider())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(emptyNewProviderList)){
            for (VerificationInfo verificationInfo:emptyNewProviderList) {
                verificationInfo.setDataType("PROVIDER");
                verificationInfo.setErrorResult("Current Provider has no official name and email.");
                warDao.insertExceptionQueue(verificationInfo);
            }
            Set<VerificationInfo> emptyProviderSet = new HashSet<>(emptyNewProviderList);
            modifiableVerificationList.removeIf(emptyProviderSet::contains);
        }
        if (CollectionUtils.isNotEmpty(modifiableVerificationList)) {
            warDao.insertVerificationInfo(modifiableVerificationList);
        }
        log.info("VerificationInfoWriterWriter end...{}", verificationList.size());
    }
}

package com.westlakefinancial.technology.partner.batch.job;

import com.westlakefinancial.technology.partner.batch.listener.JobExitCodeListener;
import com.westlakefinancial.technology.partner.batch.listener.JobStepExecutionListener;
import com.westlakefinancial.technology.partner.batch.tasklet.CopyProducersTasklets;
import com.westlakefinancial.technology.partner.batch.tasklet.InitProducersTasklets;
import com.westlakefinancial.technology.partner.batch.tasklet.InitProducersTasklets;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.job.builder.FlowBuilder;
import org.springframework.batch.core.job.flow.Flow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
public class InitProducersJob {

    @Resource
    private JobBuilderFactory jobBuilderFactory;

    @Resource
    private JobExitCodeListener jobExitCodeListener;

    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Resource
    private JobStepExecutionListener jobStepExecutionListener;

    @Resource
    private InitProducersTasklets initProducersTasklets;

    @Resource
    private CopyProducersTasklets copyProducersTasklets;

    @Resource
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Bean
    public Job initProducersProcessJob() {
        return jobBuilderFactory.get("initProducersJob")
                .incrementer(new TimestampIncrementer())
                .start(initProducersProcessFlow())
                .next(copyProducersStep())
                .build()
                .listener(jobExitCodeListener)
                .build();
    }

    @Bean
    public Flow initProducersProcessFlow() {
        return new FlowBuilder<Flow>("initProducersFlow")
                .split(taskExecutor)
                .add(
                        initProducers0(),
                        initProducers1(),
                        initProducers2(),
                        initProducers3(),
                        initProducers4(),
                        initProducers5(),
                        initProducers6(),
                        initProducers7()
                        )
                .build();
    }

    @Bean
    public Flow initProducers0() {
        return new FlowBuilder<Flow>("initProducers0").start(initProducersStep()).build();
    }

    @Bean
    public Flow initProducers1() {
        return new FlowBuilder<Flow>("initProducers1").start(initProducersStep()).build();
    }

    @Bean
    public Flow initProducers2() {
        return new FlowBuilder<Flow>("initProducers2").start(initProducersStep()).build();
    }

    @Bean
    public Flow initProducers3() {
        return new FlowBuilder<Flow>("initProducers3").start(initProducersStep()).build();
    }

    @Bean
    public Flow initProducers4() {
        return new FlowBuilder<Flow>("initProducers4").start(initProducersStep()).build();
    }

    @Bean
    public Flow initProducers5() {
        return new FlowBuilder<Flow>("initProducersData5").start(initProducersStep()).build();
    }

    @Bean
    public Flow initProducers6() {
        return new FlowBuilder<Flow>("initProducersData6").start(initProducersStep()).build();
    }

    @Bean
    public Flow initProducers7() {
        return new FlowBuilder<Flow>("initProducersData7").start(initProducersStep()).build();
    }


    @Bean
    public Step initProducersStep() {
        return stepBuilderFactory.get("initProducersStep")
                .tasklet(initProducersTasklets)
                .listener(jobStepExecutionListener)
                .build();
    }

    @Bean
    public Step copyProducersStep() {
        return stepBuilderFactory.get("copyProducersStep")
                .tasklet(copyProducersTasklets)
                .listener(jobStepExecutionListener)
                .build();
    }

}

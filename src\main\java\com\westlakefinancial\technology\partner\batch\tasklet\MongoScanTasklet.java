package com.westlakefinancial.technology.partner.batch.tasklet;

import com.mongodb.client.gridfs.model.GridFSFile;
import com.westlakefinancial.technology.partner.batch.config.Config;
import com.westlakefinancial.technology.partner.batch.domain.*;
import com.westlakefinancial.technology.partner.batch.domain.common.CallActivity;
import com.westlakefinancial.technology.partner.batch.domain.common.CallResult;
import com.westlakefinancial.technology.partner.batch.exception.MongoBusinessException;
import com.westlakefinancial.technology.partner.batch.mapper.war.WarDao;
import com.westlakefinancial.technology.partner.batch.mongo.MailTriggeredFileMongoRepo;
import com.westlakefinancial.technology.partner.batch.service.StorageHelper;
import com.westlakefinancial.technology.partner.batch.service.common.AccountCommonService;
import com.westlakefinancial.technology.partner.batch.utils.Counter;
import com.westlakefinancial.technology.partner.batch.utils.ExceptionUtils;
import com.westlakefinancial.technology.partner.batch.utils.MailUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsOperations;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * The type Mongo scan tasklet.
 */
@Component
@Slf4j
public class MongoScanTasklet implements Tasklet {

    /**
     * The constant RTF.
     */
    public static final String EXCEL = ".XLSX";

    /**
     * The Config.
     */
    protected final Config config;

    /**
     * The Mail triggered file mongo repo.
     */
    @Autowired
    protected MailTriggeredFileMongoRepo mailTriggeredFileMongoRepo;

    /**
     * The Grid fs operations.
     */
    @Autowired
    protected GridFsOperations gridFsOperations;

    /**
     * The Storage helper.
     */
    @Autowired
    protected StorageHelper storageHelper;

    @Resource
    private WarDao warDao;

    @Resource
    private AccountCommonService accountCommonService;

    @Value("${mail.verification.scanEmail}")
    private String scanEmail;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executorService;

    @Value("${mail.verification.headers}")
    private List<String> mailHeaders;

    /**
     * Instantiates a new Mongo scan tasklet.
     *
     * @param config the config
     */
    public MongoScanTasklet(Config config) {
        this.config = config;
    }

    @Override
//    @Transactional(propagation = NOT_SUPPORTED)
    public RepeatStatus execute(@NotNull StepContribution stepContribution, @NotNull ChunkContext chunkContext) {
        log.info("================= Mongo Scan Job Start, Step:{} =================", stepContribution.getStepExecution().getStepName());
        try {
            prepare();

            // get unresolved files
            List<MailTriggeredFile> messages = getUnResolvedFiles();
            if (CollectionUtils.isEmpty(messages)) {
                log.info("No unresolved message found, job finished");
                stepContribution.setExitStatus(ExitStatus.COMPLETED);
                return RepeatStatus.FINISHED;
            }
            // process email
            readAndProcessEmail(messages);
            log.info("[*] Job execution information: total message:{}, processed messages:{}, ignored message:{}, exception message:{}, processed pdf attachment:{}, pdf has vin:{}", Counter.getTotalEmailCount(), Counter.getTargetEmailCount(), Counter.getIgnoreEmailCount(), Counter.getExceptionEmailCount(), Counter.getTargetPdfCount(), Counter.getHasVin());
            stepContribution.setExitStatus(ExitStatus.COMPLETED);
        } catch (Exception e) {
            Counter.saveRuntimeErrors(ExceptionUtils.getSimpleDescription(e.getMessage()));
            stepContribution.setExitStatus(ExitStatus.FAILED);
            log.error("[*] Mail processing has failed:{}, Message:{}", e.getClass().getSimpleName(), e.getMessage());

        }
        log.info("================= Mongo Scan Job End =================");
        return RepeatStatus.FINISHED;
    }

    /**
     * Gets unresolved files.
     *
     * @return the un resolved files
     */
    protected List<MailTriggeredFile> getUnResolvedFiles() {
        // loop messages
        List<MailTriggeredFile> messages = getFilesFromMongo();
        Counter.saveTotalEmail(messages.size());
        log.info("[*] Found unresolved messages:{}", messages.size());
        return messages;
    }

    /**
     * Prepare.
     *
     * @throws IOException the io exception
     */
    private void prepare() throws IOException {
        // create processor
        log.info("[*] Preparing... temporary cache folder:{}", config.getLocalFolderPath());
        checkCacheFolder(config.getLocalFolderPath());
        log.info("[*] Preparing... init google storage");
        this.storageHelper.init();
    }

    /**
     * Check cache folder.
     *
     * @param localFolderPath the local folder path
     * @throws IOException the io exception
     */
    private void checkCacheFolder(String localFolderPath) throws IOException {
        File folder = new File(localFolderPath);
        if (!folder.exists()) {
            boolean created = folder.mkdirs();
            if (created) {
                log.info("[->] Cache folder not exist, make new directory, path:{}", localFolderPath);
            } else {
                throw new IOException("[*] Cannot create folder:" + localFolderPath);
            }
        } else {
            log.info("[->] Cache folder already exist:{}", localFolderPath);
        }
    }

    /**
     * Gets files from mongo.
     *
     * @return the files from mongo
     */
    private List<MailTriggeredFile> getFilesFromMongo() {
        return mailTriggeredFileMongoRepo.getUnresolvedFiles();
    }

    /**
     * Read and process email boolean.
     *
     * @param messages the messages
     * @return the boolean
     */
    private void readAndProcessEmail(List<MailTriggeredFile> messages) {

        messages = messages.stream().filter(r -> {
            String emailTo = MailUtils.extractEmailFromAddress(r.getRequestContext().getTo());
            return scanEmail.equalsIgnoreCase(emailTo);}).collect(Collectors.toList());
        List<Future<Void>> futures = new ArrayList<>();

        for (MailTriggeredFile message : messages) {
            futures.add(executorService.submit(() -> {
                String emailId = message.getId().toString();
                log.info("--------------------------------------------------------");
                log.info("[*] Processing Email ID:<{}>", emailId);

                List<FileInfo> fileInfos = Collections.emptyList();

                try {
                    if (!isEmailFormatValid(message)) {
                        Counter.countIgnoreEmail();
                        log.warn("[* new] Message format is invalid:{}", message.getId());
                        insertVerifyResultsError(message, null);
                        markMailCompleted(message);
                        return null;
                    }

                    log.info("[*] Message Information: Email ID:{}, ", emailId);

                    // If the sender’s email address is in the ignored list,
                    Counter.countTargetEmail();

                    // check whether message is processing target
                    if (!haveEXCELAttachment(message)) {
                        insertVerifyResultsError(message, null);
                        markMailCompleted(message);
                        return null;
                    }

                    // save attachment
                    log.info("[*] Downloading the message attachments");
                    fileInfos = saveAttachment(message);
                    log.info("[*] {} Message attachments have been downloaded", fileInfos.size());

                    // read attachment
                    log.info("[*] Analyzing message in progress");
                    fileInfos.forEach(fileInfo -> processExcel(message, fileInfo));
                    log.info("[*] Message analysis completed");

                    // mark mail completed
                    log.info("[*] Marking the message as resolved");
                    markMailCompleted(message);
                    log.info("[*] Message marked as resolved");

                } catch (Exception e) {
                    Counter.countExceptionEmail();
                    log.error("[*] Exception when processing message: Email ID:{}, Exception:{}, Message:{}", emailId, e.getClass().getSimpleName(), e.getMessage());
                } finally {
                    // clear workspace
                    if (!fileInfos.isEmpty()) {
                        log.info("[*] Cleaning up workspace in progress");
                        deleteTempFiles(config.getLocalFolderPath() + "/" + message.getId(), fileInfos);
                        log.info("[*] Workspace cleaning completed");
                    }
                    log.info("[*] Message processing {}, Email ID:{}", Counter.hasError(emailId) ? "failed" : "succeed", emailId);
                }
                return null;
            }));
        }

        for (Future<Void> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("Error waiting for task completion: {}", e.getMessage());
            }
        }

        executorService.shutdown();
        log.info("All messages processed.");
    }

    /**
     * Check email format.
     *
     * @param message the message
     */
    private boolean isEmailFormatValid(MailTriggeredFile message) {
        return message.getRequestContext() != null &&
                StringUtils.isNotBlank(message.getRequestContext().getFrom()) &&
                StringUtils.isNotBlank(message.getRequestContext().getTo()) &&
                StringUtils.isNotBlank(message.getRequestContext().getHeaders());
    }

    /**
     * Have pdf attachment boolean.
     *
     * @param message the message
     * @return the boolean
     */
    private boolean haveEXCELAttachment(MailTriggeredFile message) {
        if (message.getFiles() == null) {
            log.info("[*] The monitored email format has no attachment, Ignore");
            return false;
        } else if (message.getFiles().isEmpty()) {
            log.info("[*] Email has no attachments, Ignore");
            return false;
        }
        // settlement EXCEL match
        boolean hasTargetExcel = message.getFiles().stream().anyMatch(t -> {
            String fileName = t.getOriginalFilename().toUpperCase(Locale.ROOT);
            return fileName.endsWith(EXCEL);
        });
        if (!hasTargetExcel) {
            log.info("[*] Email attachments does not comply with the monitoring rules, Ignore");
            message.getFiles().forEach(t -> log.info("[->] {}: {}", t.getName(), t.getOriginalFilename()));
        }
        return hasTargetExcel;

    }

    /**
     * Upload to google storage.
     *
     * @param fileInfo the file info
     */
    private void uploadToGoogleStorage(FileInfo fileInfo, String emailId) {

        // generate target path
        String targetPath = config.getBucketFolder() + emailId + "/" + generateFileName();

        try {
                // upload to google storage
                storageHelper.upload(fileInfo.getPath(), targetPath);

                fileInfo.setStoragePath(targetPath);

                log.info("[->] Insert ins_email_scan_files, File Id:{}", fileInfo.getId());
        } catch (Exception e) {
            log.error("[*] Upload to google storage failed:{}, cause:{}", targetPath, e.getMessage());
            throw new MongoBusinessException(e, "Upload email attachment to google storage failed.", Collections.singletonMap("file:", fileInfo));
        }
    }

    /**
     * Generate file name string.
     *
     * @return the string
     */
    private String generateFileName() {
        return (System.currentTimeMillis() + "_" + UUID.randomUUID()).replace("-", "") + ".xlsx";
    }

    /**
     * Delete temp files.
     *
     * @param folder    the folder
     * @param fileInfos the file infos
     */
    private void deleteTempFiles(String folder, List<FileInfo> fileInfos) {
        for (FileInfo fileInfo : fileInfos) {
            Path path = Paths.get(fileInfo.getPath());
            try {
                boolean isDeleted = Files.deleteIfExists(path);
                if (isDeleted) {
                    log.info("[->] file:{} was deleted", fileInfo);
                } else {
                    log.warn("[->] file not exist:{}", fileInfo);
                }
            } catch (IOException e) {
                log.error("[->] Error occurred when deleting file:{}, cause:{}", fileInfo, e.getMessage());
            }
        }

        try {
            boolean isDeleted = Files.deleteIfExists(Paths.get(folder));
            if (isDeleted) {
                log.info("[->] folder:{} was deleted", folder);
            } else {
                log.warn("[->] folder not exist:{}", folder);
            }
        } catch (IOException e) {
            log.error("[->] Error occurred when deleting folder:{}, cause:{}", folder, e.getMessage());
        }
    }

    /**
     * Mark mail completed.
     *
     * @param message the message
     */
    private void markMailCompleted(MailTriggeredFile message) {
        message.setIsResolved("Y");
        mailTriggeredFileMongoRepo.updateFile(message);
    }

    /**
     * Save attachment list.
     *
     * @param message the message
     * @return the list
     */
    private List<FileInfo> saveAttachment(MailTriggeredFile message) {
        // save attachment
        List<FileInfo> filePaths = new ArrayList<>();

        // get all file information
        List<MailTriggeredFile.File> files = message.getFiles();

        files.forEach(f -> {
            try {

                // get file content from gridFS
                GridFSFile gridFSFile = gridFsOperations.findOne(new Query(Criteria.where("_id").is(f.getFileId())));
                if (gridFSFile == null) {
                    throw new IllegalStateException("File not exist, id:" + f.getFileId() + " name:" + f.getOriginalFilename());
                }

                // check folder
                String dir = config.getLocalFolderPath() + "/" + message.getId();
                File folder = new File(dir);

                // if folder not exist, then create one
                if (!folder.exists()) {
                    boolean created = folder.mkdirs();
                    if (!created) {
                        throw new IllegalStateException("Create folder failed:" + dir);
                    }
                }

                // copy file to dest folder
                String fileName = getValidFileName(f);
                String filePath = String.format("%s/%s", dir, fileName);
                InputStream inputStream = gridFsOperations.getResource(gridFSFile).getInputStream();
                Path path = Paths.get(filePath);
                Files.copy(inputStream, path, StandardCopyOption.REPLACE_EXISTING);
                log.info("[->] Downloaded:{}", filePath);

                // save file info
                    filePaths.add(new FileInfo(f.getFileId(), filePath, f.getName(), fileName));

            } catch (Exception e) {
                log.error("[*] Error occurred when saving attachment:{}, cause:{}", f.getOriginalFilename(), e.getMessage());
                throw new MongoBusinessException(e, "Saving email attachment.", Collections.singletonMap("file:", f));
            }
        });

        return filePaths;
    }

    /**
     * Gets valid file name.
     *
     * @param file the file
     * @return the valid file name
     */
    private String getValidFileName(MailTriggeredFile.File file) {
        String vfn = file.getOriginalFilename().replaceAll("[\\\\/:*?\"<>|!@#$%^&()+]", "");
        String finalName = StringUtils.isNotBlank(vfn) ? vfn : file.getFileId() + file.getExt();

        if (!vfn.equals(file.getOriginalFilename())) {
            log.info("[->] Attachment file name contains invalid char, rename <{}> to <{}>", file.getOriginalFilename(), finalName);
        }
        return finalName;
    }

        private void processExcel(MailTriggeredFile message, FileInfo fileInfo) {

            if (!fileInfo.getOriginalName().toUpperCase(Locale.ROOT).endsWith(EXCEL)) {
                insertVerifyResultsError(message, fileInfo);
                return;
            }
            try (FileInputStream fis = new FileInputStream(fileInfo.getPath());
                 Workbook workbook = new XSSFWorkbook(fis)) {

                Sheet sheet = workbook.getSheetAt(0);
                Row headerRow = sheet.getRow(0);

                if (headerRow == null) {
                    log.warn("Header is empty!");
                    insertVerifyResultsError(message,fileInfo);
                    return;
                }

                boolean headersMatch = true;
                if (headerRow.getLastCellNum() != mailHeaders.size()) {
                    headersMatch = false;
                } else {
                    for (int i = 0; i < mailHeaders.size(); i++) {
                        Cell cell = headerRow.getCell(i);
                        if (cell == null
                                || cell.getCellType() == CellType.BLANK
                                || !mailHeaders.get(i).equalsIgnoreCase(cell.getStringCellValue().trim())) {
                            headersMatch = false;
                            break;
                        }
                    }
                }
                List<Boolean> isSuccessList = new ArrayList<>();
                if (headersMatch) {
                    VerifyResults verifyResults = new VerifyResults();
                    for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                        Row row = sheet.getRow(rowIndex);
                        if (row != null) {
                            VerificationInfo verificationInfo = warDao.getVerificationInfoByEmailInfo(formatAccNbrValue(row.getCell(0)));
                                for (int colIndex = 0; colIndex < row.getLastCellNum(); colIndex++) {
                                    verifyResults.setAccNbr(formatAccNbrValue(row.getCell(0)));
                                    if (verificationInfo == null){
                                        verifyResults.setVin(formatCellValue(row.getCell(1)));
                                        verifyResults.setVehicleYear(formatCellValue(row.getCell(2)));
                                        verifyResults.setVehicleMake(formatCellValue(row.getCell(3)));
                                        verifyResults.setVehicleModel(formatCellValue(row.getCell(4)));
                                        verifyResults.setCusFirstName(formatCellValue(row.getCell(5)));
                                        verifyResults.setCusLastName(formatCellValue(row.getCell(6)));
                                        verifyResults.setActiveDt(formatCellValue(row.getCell(7)));
                                        verifyResults.setContractDt(formatCellValue(row.getCell(8)));
                                        verifyResults.setDealer(formatCellValue(row.getCell(9)));
                                        verifyResults.setDealerCode(formatCellValue(row.getCell(10)));
                                        verifyResults.setDealerState(formatCellValue(row.getCell(11)));
                                        verifyResults.setProvider(formatCellValue(row.getCell(12)));
                                        verifyResults.setPremium(formatCellValue(row.getCell(13)));
                                    }else {
                                        verifyResults.setVin(verificationInfo.getVin());
                                        verifyResults.setVehicleYear(verificationInfo.getVehicleYear());
                                        verifyResults.setVehicleMake(verificationInfo.getVehicleMake());
                                        verifyResults.setVehicleModel(verificationInfo.getVehicleModel());
                                        verifyResults.setCusFirstName(verificationInfo.getCusFirstName());
                                        verifyResults.setCusLastName(verificationInfo.getCusLastName());
                                        verifyResults.setActiveDt(verificationInfo.getActiveDt());
                                        verifyResults.setContractDt(verificationInfo.getContractDt());
                                        verifyResults.setDealer(verificationInfo.getDealer());
                                        verifyResults.setDealerCode(verificationInfo.getDealerCode());
                                        verifyResults.setDealerState(verificationInfo.getDealerState());
                                        verifyResults.setProvider(verificationInfo.getProvider());
                                        verifyResults.setPremium(verificationInfo.getPremium());
                                        verifyResults.setDealerEmail(verificationInfo.getDealerEmail());
                                        verifyResults.setProviderEmail(verificationInfo.getProviderEmail());
                                        verifyResults.setLastSentDt(verificationInfo.getLastSentDt());
                                        verifyResults.setNumSent(verificationInfo.getNumSent());
                                        verifyResults.setAadId(verificationInfo.getAadId());
                                        verifyResults.setCompany(verificationInfo.getCompany());
                                    }
                                    verifyResults.setAllDataCorrect(formatCellValue(row.getCell(14)));
                                    verifyResults.setActivated(formatCellValue(row.getCell(15)));
                                    verifyResults.setNotes(formatCellValue(row.getCell(16)));
                                }
                            Boolean isSuccess = checkRequired(verifyResults, verificationInfo, message);
                            isSuccessList.add(isSuccess);
                        }
                    }
                    if (isSuccessList.stream().anyMatch(r -> r == false)) {
                        insertVerifyResultsError(message, fileInfo);
                    }
                    return;
                }
                insertVerifyResultsError(message, fileInfo);

            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        private String formatCellValue(Cell cell){
            String cellValue;
            if (cell != null) {
                switch (cell.getCellType()) {
                    case STRING:
                        cellValue = cell.getStringCellValue();
                        break;
                    case NUMERIC:
                        cellValue = String.valueOf(cell.getNumericCellValue());
                        break;
                    case BOOLEAN:
                        cellValue = String.valueOf(cell.getBooleanCellValue());
                        break;
                    case FORMULA:
                        cellValue = cell.getCellFormula();
                        break;
                    default:
                        cellValue = "";
                        break;
                }
            } else {
                cellValue = "";
            }
            return cellValue;
        }

        private String formatAccNbrValue(Cell cell) {
            String cellValue;
            try {
                if (cell != null) {
                    switch (cell.getCellType()) {
                        case STRING:
                            cellValue = cell.getStringCellValue();
                            break;
                        case NUMERIC:
                            cellValue = String.valueOf((long) cell.getNumericCellValue());
                            break;
                        default:
                            cellValue = "";
                            break;
                    }
                } else {
                    cellValue = "";
                }
            } catch (Exception e) {
                log.error("Error occurred when format accNbr value, cause:{}", e.getMessage());
                cellValue = "";
            }
            return cellValue;
        }

    private boolean checkRequired(VerifyResults verifyResults,VerificationInfo verificationInfo,MailTriggeredFile message) {
        if (verificationInfo == null){
            verifyResults.setDataType("DATA_MESSING");
            verifyResults.setEmailId(message.getId().toHexString());
            verifyResults.setErrorResult("Warranty activation failed -mismatch data");
            warDao.insertExceptionQueueByVerifyResults(verifyResults);
            return false;
        }
        warDao.insertVerifyResults(verifyResults);
        boolean isCurrent = true;
        List<String> errorResultList = new LinkedList<>();
        String errorResult;
        // Check required fields
        if (StringUtils.isEmpty(verifyResults.getAllDataCorrect())){
            errorResult = "Warranty company failed to confirm activation";
            errorResultList.add(errorResult);
            isCurrent = false;
        }else if (!"Y".equals(verifyResults.getAllDataCorrect())){
            errorResult = verifyResults.getNotes();
            errorResultList.add(errorResult);
            isCurrent = false;
        }else if (verifyResults.getAllDataCorrect().length() > 2){
            verifyResults.setAllDataCorrect(null);
            errorResult =  "Warranty company failed to confirm activation";
            errorResultList.add(errorResult);
            isCurrent = false;
        }

        if (StringUtils.isEmpty(verifyResults.getActivated())){
            errorResult = "Warranty company failed to confirm activation";
            errorResultList.add(errorResult);
            isCurrent = false;
        }else if (!"Y".equals(verifyResults.getActivated())){
            errorResult = verifyResults.getNotes();
            errorResultList.add(errorResult);
            isCurrent = false;
        }else if (verifyResults.getActivated().length() > 2){
            verifyResults.setActivated(null);
            errorResult =  "Warranty company failed to confirm activation";
            errorResultList.add(errorResult);
            isCurrent = false;
        }

        if (verifyResults.getNotes() != null && verifyResults.getNotes().length() > 500){
            errorResult =  "Notes is too long";
            errorResultList.add(errorResult);
            isCurrent = false;
        }

        String errorMsg = errorResultList.stream().distinct().collect(Collectors.joining(","));
        if (!isCurrent) {
            verifyResults.setDataType("DATA_VERIFICATION");
            verifyResults.setEmailId(message.getId().toHexString());
            verifyResults.setErrorResult(errorMsg);
            warDao.insertExceptionQueueByVerifyResults(verifyResults);
            return false;
        }

        CallActivity callActivity = new CallActivity();
        if ("Y".equals(verifyResults.getAllDataCorrect())
                && "Y".equals(verifyResults.getActivated())) {
            callActivity.setAadId(verifyResults.getAadId());
            callActivity.setAccNbr(verifyResults.getAccNbr());
            callActivity.setActionCd("SCV");
            callActivity.setResultCd("ACT");
            callActivity.setVinNbr(verifyResults.getVin());
            callActivity.setObjectLevel("ACC");
            callActivity.setComment("Add this SCV/AVT to the account and the condition would be SC_PV_ACT");

            if (StringUtils.isEmpty(callActivity.getAadId())) {
                errorResult =  "Warranty activation failed -mismatch data";
                errorResultList.add(errorResult);
                return false;
            }

            Integer activatedCount = warDao.getActivatedCount(verifyResults.getAadId());
            if (activatedCount == null || activatedCount == 0) {
                callActivity(callActivity, "SIMCOE-WARRANTY-BATCH", true);
            }
            warDao.updateWarVerificationEnabled(callActivity.getAccNbr());
            warDao.updateWarVerificationErrorEnabled(callActivity.getAccNbr());
            warDao.updateVerifResultsEnabled(callActivity.getAccNbr());
        }
        return isCurrent;
    }

    private void insertVerifyResultsError(MailTriggeredFile message,FileInfo fileInfo){
        VerifyResultsError verifyResultsError = new VerifyResultsError();

        //Get email send date
        String[] headers = message.getRequestContext().getHeaders().split("\\r?\\n");
        String date = extractReceivedDate(headers);

        verifyResultsError.setEmailSendDate(dateFormatExample(date));
        verifyResultsError.setEmailFrom(message.getRequestContext().getFrom());
        verifyResultsError.setEmailTo(message.getRequestContext().getTo());
        verifyResultsError.setEmailHtml(message.getRequestContext().getHtml());
        verifyResultsError.setEmailSubject(message.getRequestContext().getSubject());
        verifyResultsError.setEmailId(message.getId().toHexString());
        if (fileInfo != null) {
            uploadToGoogleStorage(fileInfo,message.getId().toHexString());
            verifyResultsError.setGooglePath(fileInfo.getStoragePath());
        }
        warDao.insertVerifyResultsError(verifyResultsError);
    }

    public void callActivity(CallActivity callActivity, String agentId, Boolean conditionFlag) {

        CallResult callResult = new CallResult();
        Map<String, String> dayBreakParam = new HashMap<>();
        String[] accNbrArray = callActivity.getAccNbr().split(",");
        String[] aadIdArray = callActivity.getAadId().split(",");
        callResult.setDbkActionCd(callActivity.getActionCd());
        callResult.setDbkResultCd(callActivity.getResultCd());


        try {
            for (int i = 0; i < accNbrArray.length; i++) {
                dayBreakParam.put("condition", "SC_PV_ACT");
                dayBreakParam.put("aadId", aadIdArray[i]);
                dayBreakParam.put("accNbr", accNbrArray[i]);
                dayBreakParam.put("timeFlag", "Y");
                dayBreakParam.put("userName", agentId);
                dayBreakParam.put("vinNbr", callActivity.getVinNbr());
                dayBreakParam.put("comment", callActivity.getComment());
                if (org.apache.commons.lang3.StringUtils.isEmpty(callActivity.getComment())) {
                    dayBreakParam.put("comment", callActivity.getComment());
                }
                if (StringUtils.isNotEmpty(dayBreakParam.get("comment")) && conditionFlag) {
                        accountCommonService.sendComment(buildActivityComment(callActivity, agentId, dayBreakParam.get("comment")));
                }
                dayBreakParam.put("catType", callActivity.getActionCd());
                dayBreakParam.put("crtType", callActivity.getResultCd());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(callResult.getDbkActionCd()) && org.apache.commons.lang3.StringUtils.isNotEmpty(callResult.getDbkResultCd())) {
                    dayBreakParam.put("catType", callResult.getDbkActionCd().toUpperCase());
                    dayBreakParam.put("crtType", callResult.getDbkResultCd().toUpperCase());
                    if (conditionFlag) {
                        accountCommonService.sendCallActivity(dayBreakParam);
                    }
                }
            }
        } catch (NoSuchAlgorithmException | IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    private Map<String, String> buildActivityComment(CallActivity callActivity, String userName, String comment) {
        Map<String, String> commentMap = new HashMap<>();
        commentMap.put("aadId", callActivity.getAadId());
        commentMap.put("accNbr", callActivity.getAccNbr());
        commentMap.put("userName", userName);
        commentMap.put("comment", comment);
        commentMap.put("commentTypeCd", "REG");
        commentMap.put("commentSubTypeCd", "TTL");
        commentMap.put("acmCommentImpInd", "N");
        return commentMap;
    }

    /**
     * Extract received date string.
     *
     * @param headers the headers
     * @return the string
     */
    private String extractReceivedDate(String[] headers) {
        // match format:
        // Received: from mx0b-0028fb01.pphosted.com (mxd [**************]) by mx.sendgrid.net with ESMTP id c-Ge1pUuTy25jxziKbhVYw for <<EMAIL>>; Mon, 14 Aug 2023 18:31:34.031 +0000 (UTC)
        for (int i = 0; i < headers.length; i++) {
            if (headers[i].startsWith("Received:")) {
                String date = headers[i].substring(headers[i].indexOf(";") + 1).trim();
                log.debug("[->] Extract Received Date:{}", date);
                return headers[i].substring(headers[i].indexOf(";") + 1).trim();
            }
        }

        return "";
    }


    private String dateFormatExample(String dateStr) {

        String originalDateStr = dateStr;
        String formattedDateStr = "";

        SimpleDateFormat originalFormat = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss.SSS Z", Locale.ENGLISH);
        String dateWithoutTimezone = originalDateStr.replaceAll(" \\([A-Z]+\\)", "");
        originalFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

        SimpleDateFormat targetFormat = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");

        try {
            Date date = originalFormat.parse(dateWithoutTimezone);

             formattedDateStr = targetFormat.format(date);

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return formattedDateStr;
    }
}


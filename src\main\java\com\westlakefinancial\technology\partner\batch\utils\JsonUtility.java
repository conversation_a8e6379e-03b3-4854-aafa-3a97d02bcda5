package com.westlakefinancial.technology.partner.batch.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class JsonUtility {
    private JsonUtility() {
        throw new IllegalStateException("Utility class");
    }

    public static <T> String objectToJson(T source) throws IOException {
        return getObjectMapper().writeValueAsString(source);
    }

    public static <T> T jsonToObject(String json, Class<?> dataType, Class<?>... genericityTypes)
            throws IOException {
        ObjectMapper objectMapper = getObjectMapper();
        JavaType type = objectMapper.getTypeFactory()
                .constructParametricType(dataType, genericityTypes);
        return getObjectMapper().readValue(json, type);
    }

    public static <T> T objectToClass(Object obj, Class<T> dataType) throws IOException {
        Map<String, Object> map = jsonToObject(objectToJson(obj),
                new TypeReference<Map<String, Object>>() {
                });

        return jsonToObject(objectToJson(map), dataType);
    }

    public static <T> T cast(Object obj, TypeReference<T> ref)
            throws IOException {
        return jsonToObject(objectToJson(obj), (TypeReference<Map<String, Object>>) ref);
    }


    public static <T> T jsonToObject(String json, TypeReference<Map<String, Object>> dataType) throws IOException {
        return (T) getObjectMapper().readValue(json, dataType);
    }

    public static ObjectMapper getObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        return objectMapper;
    }
}

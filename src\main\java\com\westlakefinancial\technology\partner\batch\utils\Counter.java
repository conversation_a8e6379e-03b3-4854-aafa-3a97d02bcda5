package com.westlakefinancial.technology.partner.batch.utils;


import com.westlakefinancial.technology.partner.batch.domain.MongoErrorInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The type Counter.
 *
 * <AUTHOR>
 */
public class Counter {
    /**
     * The Total email count.
     */
    private static Integer totalEmailCount = 0;
    /**
     * The Target email count.
     */
    private static Integer targetEmailCount = 0;
    /**
     * The Target pdf count.
     */
    private static Integer targetPdfCount = 0;
    /**
     * w
     * Ignore email count.
     */
    private static Integer ignoreEmailCount = 0;
    /**
     * The Exception email count.
     */
    private static Integer exceptionEmailCount = 0;
    /**
     * The Has VIN count.
     */
    private static Integer hasVinCount = 0;

    /**
     * The constant errors.
     */
    private static final List<MongoErrorInfo> errors = new ArrayList<>();

    /**
     * The constant errors.
     */
    private static final List<String> runtimeErrors = new ArrayList<>();

    private static Map<String, String> params = new HashMap<>();

    private Counter() {

    }

    /**
     * Save total email.
     *
     * @param cnt the cnt
     */
    public static void saveTotalEmail(Integer cnt) {
        totalEmailCount = cnt;
    }

    /**
     * Count target email.
     */
    public static void countTargetEmail() {
        targetEmailCount++;
    }

    /**
     * Count target pdf.
     */
    public static void countTargetPdf() {
        targetPdfCount++;
    }

    /**
     * Count ignore email.
     */
    public static void countIgnoreEmail() {
        ignoreEmailCount++;
    }

    /**
     * Count exception email.
     */
    public static void countExceptionEmail() {
        exceptionEmailCount++;
    }

    /**
     * Count has vin pdf.
     */
    public static void countHasVinPdf() {
        hasVinCount++;
    }

    /**
     * Init.
     */
    public static void init() {
        totalEmailCount = 0;
        targetEmailCount = 0;
        targetPdfCount = 0;
        ignoreEmailCount = 0;
        exceptionEmailCount = 0;
        hasVinCount = 0;
    }

    /**
     * Gets total email count.
     *
     * @return the total email count
     */
    public static Integer getTotalEmailCount() {
        return totalEmailCount;
    }

    /**
     * Gets target email count.
     *
     * @return the target email count
     */
    public static Integer getTargetEmailCount() {
        return targetEmailCount;
    }

    /**
     * Gets target pdf count.
     *
     * @return the target pdf count
     */
    public static Integer getTargetPdfCount() {
        return targetPdfCount;
    }

    /**
     * Gets ignore email count.
     *
     * @return ignored email count
     */
    public static Integer getIgnoreEmailCount() {
        return ignoreEmailCount;
    }

    /**
     * Gets exception email count.
     *
     * @return the exception email count
     */
    public static Integer getExceptionEmailCount() {
        return exceptionEmailCount;
    }

    /**
     * Gets has vin.
     *
     * @return the has vin
     */
    public static Integer getHasVin() {
        return hasVinCount;
    }

    /**
     * Save error.
     *
     * @param msgId   the msg id
     * @param subject the subject
     * @param from    the from
     * @param date    the date
     * @param error   the error
     */
    public static void saveError(String msgId, String subject, String from, String date, Exception error) {
        String message = String.format("<%s>:%s", error.getClass().getSimpleName(), ExceptionUtils.getSimpleDescription(error.getMessage()));
        errors.add(new MongoErrorInfo(msgId, subject, from, date, message));
    }

    /**
     * Has error.
     *
     * @param msgId   the msg id
     */
    public static boolean hasError(String msgId) {
        return errors.stream().anyMatch(t -> t.getEmailId().equals(msgId));
    }

    /**
     * Save runtime error.
     *
     * @param error the error
     */
    public static void saveRuntimeErrors(String error) {
        runtimeErrors.add(error);
    }

    public static void saveRunParam(Map<String, String> runParam) {
        params = runParam;
    }

    /**
     * Gets errors.
     *
     * @return the errors
     */
    public static List<MongoErrorInfo> getErrors() {
        return errors;
    }

    /**
     * Gets runtime errors.
     *
     * @return the runtime errors
     */
    public static List<String> getRuntimeErrors() {
        return runtimeErrors;
    }

    public static Map<String, String> getRunParam() {
        return params;
    }
}

package com.westlakefinancial.technology.partner.batch.utils;

import com.westlakefinancial.technology.commonUtil.util.MyX509TrustManager;
import lombok.extern.slf4j.Slf4j;

import javax.net.ssl.*;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.X509Certificate;

@Slf4j
public class RestUtils {

	private final static int MAX_RETRY = 3;

    private final static HostnameVerifier DO_NOT_VERIFY = new HostnameVerifier() {
        @Override
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    };

	/**
	 * Send http/https
	 * 
	 * @param urls
	 * @param param
	 * @param token
	 * @param method
	 * @return result
	 */
    public static String send(String urls, String param, String token, String method){
        String result = "";

        for (int i =0; i<MAX_RETRY; i++) {
            if (urls.toUpperCase().startsWith("HTTPS")) {
                result = sendHttps(urls, param, token, method);
            } else {
                result = sendHttp(urls,param, token, method);
            }

            if (!result.isEmpty()) {
                break;
            }
        }
        return result;
    }

    private static String sendHttp(String urls, String param, String token, String method){
        StringBuffer sb = new StringBuffer();
        OutputStream out = null;
        try {
            trustAllHosts();
            URL url = new URL(urls);

            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setUseCaches(false);
            conn.setRequestMethod(method);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestProperty("Authorization", "Bearer " + token);
            conn.setRequestProperty("content-type", "application/json");
            conn.setRequestProperty("Accept", "application/json");
            out = conn.getOutputStream();
            out.write(param.getBytes());
            out.flush();
            out.close();
            if (conn.getResponseCode() != 200) {
                throw new RuntimeException("Failed : HTTP Error code : "
                        + conn.getResponseCode());
            }
            InputStreamReader in = new InputStreamReader(conn.getInputStream());
            BufferedReader br = new BufferedReader(in);

            String output;
            while ((output = br.readLine()) != null) {
                sb.append(output).append("\n");
            }
            conn.disconnect();

        } catch (Exception e) {
            log.error("-----> Exception in Calling :- " + urls + e.getMessage());
        }
        return sb.toString();
    }

    private static String sendHttps(String urls, String param, String token, String method) {
        StringBuffer sb = new StringBuffer();
        DataOutputStream dataOut = null;
        try {
            trustAllHosts();
            URL url = new URL(urls);
            HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();
            conn.setHostnameVerifier(DO_NOT_VERIFY);
            conn.setUseCaches(false);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestMethod(method);
            conn.setRequestProperty("Authorization", "Bearer " + token);
            conn.setRequestProperty("Accept", "application/json");
            conn.setRequestProperty("Charset","UTF-8");
            conn.setRequestProperty("content-type", "application/json");
            dataOut = new DataOutputStream(conn.getOutputStream());
            dataOut.writeBytes(param);
            dataOut.flush();
            dataOut.close();
            if (conn.getResponseCode() != 200) {
                throw new RuntimeException("Failed : HTTP Error code : " + conn.getResponseCode());
            }
            InputStreamReader in = new InputStreamReader(conn.getInputStream());
            BufferedReader br = new BufferedReader(in);

            String output;
            while ((output = br.readLine()) != null) {
                sb.append(output).append("\n");
            }
            conn.disconnect();

        } catch (Exception e) {
            log.error("-----> Exception in Calling:- " + urls + e.getMessage());
        }
        log.info("Message: " + sb);
        return sb.toString();
    }

    public static void trustAllHosts() {

        TrustManager[] trustAllCerts = new TrustManager[]{new MyX509TrustManager() {
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[]{};
            }

            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
            }
        }};

        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());

            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

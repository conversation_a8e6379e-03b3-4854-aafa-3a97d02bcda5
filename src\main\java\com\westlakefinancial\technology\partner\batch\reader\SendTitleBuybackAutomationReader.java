package com.westlakefinancial.technology.partner.batch.reader;

import com.westlakefinancial.technology.partner.batch.domain.TitleBuyBackAutomationDTO;
import com.westlakefinancial.technology.partner.batch.mapper.war.WarDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemReader;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.util.LinkedList;


@Component
@Slf4j
public class SendTitleBuybackAutomationReader implements ItemReader<TitleBuyBackAutomationDTO> {

    @Resource
    private WarDao warDao;

    private LinkedList<TitleBuyBackAutomationDTO> titleBuyBackAutomationDTOLinkedList;

    @Override
    public TitleBuyBackAutomationDTO read() {

        log.info("titleBuyBackAutomationList start");

        if (null ==  titleBuyBackAutomationDTOLinkedList ) {
            titleBuyBackAutomationDTOLinkedList = new LinkedList<>();
            titleBuyBackAutomationDTOLinkedList.addAll(warDao.getTitleBuyBackAutomationList());
        }

        if (CollectionUtils.isEmpty(titleBuyBackAutomationDTOLinkedList)) {
            return null;
        }

        log.info("titleBuyBackAutomationList end");
        return titleBuyBackAutomationDTOLinkedList.poll();
    }
}

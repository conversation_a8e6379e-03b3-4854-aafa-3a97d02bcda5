package com.westlakefinancial.technology.partner.batch.tasklet;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.interactive.form.PDAcroForm;
import org.apache.pdfbox.pdmodel.interactive.form.PDField;
import com.westlakefinancial.technology.partner.batch.constants.CommonConstant;
import com.westlakefinancial.technology.partner.batch.domain.*;
import com.westlakefinancial.technology.partner.batch.mapper.alloy.read.AlloyReadDao;
import com.westlakefinancial.technology.partner.batch.mapper.alloy.write.AlloyWriteDao;
import com.westlakefinancial.technology.partner.batch.mapper.war.WarDao;
import com.westlakefinancial.technology.partner.batch.service.CommonSendMailService;
import com.westlakefinancial.technology.partner.batch.utils.JsonUtility;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.mail.EmailException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Component
public class AncillaryProductCancellationNoticeTasklets implements Tasklet {

    @Resource
    private BatchCommonDto batchCommonDto;

    @Resource
    private AlloyReadDao alloyReadDao;

    @Resource
    private AlloyWriteDao alloyWriteDao;

    @Resource
    private WarDao warDao;

    @Resource(name = "sendCommonMailService")
    private CommonSendMailService sendMailService;

    @Value("${mail.ancillary.error.from}")
    private String errorMailFrom;
    @Value("${mail.ancillary.error.to}")
    private String errorMailTo;
    @Value("${mail.ancillary.error.cc}")
    private String errorMailCc;
    @Value("${mail.host}")
    private String emailHost;
    @Value("${mail.port}")
    private String emailPort;
    @Value("${mail.ancillary.from}")
    private String emailFrom;
    @Value("${mail.ancillary.cc}")
    private String emailCc;
    @Value("${mail.ancillary.test.mode}")
    private String testMode;
    @Value("${mail.ancillary.test.email}")
    private String testEmail;
    @Value("${simcoe.filenetInternal.url}")
    private String filenetInternalurl;
    private final RestTemplate restTemplate;
    private static final String mailHeaders = "AccNbr,Product,Contract_Id,Provider,Note";
    private static final String WFS_TEMPLATE = "NH_Cancellation_Letter_WFS_Provider.pdf";
    private static final String WFI_TEMPLATE = "NH_Cancellation_Letter_WFI_Provider.pdf";
    private static final String WPM_TEMPLATE = "NH_Cancellation_Letter_WPM_Provider.pdf";
    private static final String INOP_WFS_TEMPLATE = "NH_Cancellation_Letter_WFS_Provider_INOP.pdf";
    private static final String INOP_WFI_TEMPLATE = "NH_Cancellation_Letter_WFI_Provider_INOP.pdf";
    private static final String INOP_WPM_TEMPLATE = "NH_Cancellation_Letter_WPM_Provider_INOP.pdf";

    public AncillaryProductCancellationNoticeTasklets(RestTemplateBuilder restTemplate) {
        this.restTemplate = restTemplate.build();
    }

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) {
        try {
            log.info("Call AncillaryProductCancellationNoticeTasklets start");
            List<AncillaryProductInfo> ancillaryProductInfoList = alloyReadDao.getAncillaryProductInfo();
            if (CollectionUtils.isEmpty(ancillaryProductInfoList)) {
                return RepeatStatus.FINISHED;
            }
            ancillaryProductInfoList = checkAndSendErrorEmail(ancillaryProductInfoList);
            try {
                sendProviderEmail(ancillaryProductInfoList);
            } catch (EmailException e) {
                throw new RuntimeException(e);
            }
        } catch (Exception e) {
            log.error("call AncillaryProductCancellationNoticeTasklets error :{0}" + e);
            ErrorInfo errorInfo = new ErrorInfo();
            errorInfo.setErrorStep("refCtxCancelMileStep");
            errorInfo.setErrorMsg(e.toString());
            batchCommonDto.getErrorInfos().add(errorInfo);
        }
        log.info("Call AncillaryProductCancellationNoticeTasklets end");
        return RepeatStatus.FINISHED;
    }


    public File generateExcel(List<ProviderInfo> providerInfoList, String mailHeaders) throws ExcelGenerationException {
        if (providerInfoList == null || mailHeaders == null || mailHeaders.isEmpty()) {
            throw new IllegalArgumentException("Provider info list and mail headers cannot be null or empty");
        }

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Ancillary Product Cancellation Notice Info");

            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 12);
            headerFont.setColor(IndexedColors.WHITE.getIndex());

            CellStyle headerCellStyle = workbook.createCellStyle();
            headerCellStyle.setFont(headerFont);
            headerCellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
            headerCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
            headerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            Row headerRow = sheet.createRow(0);

            String[] headers = mailHeaders.split(",");
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerCellStyle);
            }

            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            int rowNum = 1;
            for (ProviderInfo providerInfo : providerInfoList) {
                String note = "";
                if (StringUtils.isBlank(providerInfo.getProvider())){
                    note = "Provider is empty";
                }else if(StringUtils.isBlank(providerInfo.getEmail())){
                    note = "Provider email is empty";
                }
                Row dataRow = sheet.createRow(rowNum++);
                dataRow.createCell(0).setCellValue(providerInfo.getAccNbr());
                dataRow.createCell(1).setCellValue(providerInfo.getProduct());
                dataRow.createCell(2).setCellValue(providerInfo.getContractId());
                dataRow.createCell(3).setCellValue(providerInfo.getProvider());
                dataRow.createCell(4).setCellValue(note);
            }

            Path tempFilePath = Files.createTempFile("empty_provider_email_", ".xlsx");
            File tempFile = tempFilePath.toFile();

            try (FileOutputStream fileOut = new FileOutputStream(tempFile)) {
                workbook.write(fileOut);
            }

            return tempFile;
        } catch (IOException e) {
            log.error(String.format("Generate Excel Error: %s", e.getMessage()));
            throw new ExcelGenerationException("Failed to generate Excel file", e);
        }
    }

    public static class ExcelGenerationException extends Exception {
        public ExcelGenerationException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    public File fillTemplate(Map<String, String> map) throws IOException {
        String templateFile = selectTemplateName(map);
        if (StringUtils.isBlank(templateFile)){
            return null;
        }
        InputStream inputStream = this.getClass().getResourceAsStream("/template/warranty/pdf/" + templateFile);
        if (inputStream == null) {
            throw new FileNotFoundException("Template file not found: " + templateFile);
        }
        Path tempFilePath = Files.createTempFile("Ancillary_Product_Cancellation_Notice_", ".pdf");
        File tempFile = tempFilePath.toFile();

        try (PDDocument document = PDDocument.load(inputStream)) {
            PDDocumentCatalog docCatalog = document.getDocumentCatalog();
            PDAcroForm acroForm = docCatalog.getAcroForm();
            if (acroForm != null) {
                for (PDField field : acroForm.getFields()) {
                    String fieldName = field.getFullyQualifiedName();
                    String fieldValue = map.get(fieldName);
                    if (fieldValue != null) {
                        field.setValue(fieldValue);
                    }
                }
                acroForm.flatten(); 
            }
            document.save(tempFile);
        }

        map.put("createdBy","Simcoe-Warranty-Batch");
        map.put("lastUpdatedBy","Simcoe-Warranty-Batch");
        map.put("attachFileNames", tempFile.getName());
        map.put("message",MessageFormat.format("send providerEmail: {0}", map.get("emailTo")));

        if (!"Y".equals(testMode)) {
            insertProviderLetterHistory(map);
            insertEventLog(map);
            sendFileNet(map.get("accNbr"), tempFile);
        }
        return tempFile;
    }


    private List<AncillaryProductInfo> checkAndSendErrorEmail(List<AncillaryProductInfo> ancillaryProductInfoList) throws EmailException {
        List<ProviderInfo> emptyProviderInfo = new ArrayList<>();
        List<AncillaryProductInfo> backupList = new CopyOnWriteArrayList<>(ancillaryProductInfoList);
        ancillaryProductInfoList.forEach(ancillaryProductInfo -> {
            ProviderInfo providerInfo = alloyReadDao.getProvidersByContractId(ancillaryProductInfo.getContractId());
            if (providerInfo == null){
                providerInfo = new ProviderInfo();
                providerInfo.setAccNbr(ancillaryProductInfo.getAccNbr());
                providerInfo.setProduct(ancillaryProductInfo.getProduct());
                providerInfo.setContractId(ancillaryProductInfo.getContractId());
                emptyProviderInfo.add(providerInfo);
                backupList.remove(ancillaryProductInfo);
                return;
            }
            if (StringUtils.isNotBlank(providerInfo.getProviderId())
            && providerInfo.getDealerId().contains("WPM_CARCAPITAL")){
                backupList.remove(ancillaryProductInfo);
                return;
            }
            if (StringUtils.isEmpty(providerInfo.getEmail())
                    || StringUtils.isEmpty(providerInfo.getProvider())) {
                providerInfo.setAccNbr(ancillaryProductInfo.getAccNbr());
                providerInfo.setProduct(ancillaryProductInfo.getProduct());
                providerInfo.setContractId(ancillaryProductInfo.getContractId());
                emptyProviderInfo.add(providerInfo);
                backupList.remove(ancillaryProductInfo);
                return;
            }

            ancillaryProductInfo.setProviderName(providerInfo.getProvider());
            ancillaryProductInfo.setCompanyAddress(providerInfo.getCompanyAddress());
            ancillaryProductInfo.setCityStateZip(providerInfo.getCityStateZip());
            ancillaryProductInfo.setCompanyCity(providerInfo.getCompanyCity());
            ancillaryProductInfo.setCompanyStateCd(providerInfo.getCompanyStateCd());
            ancillaryProductInfo.setCompanyZip(providerInfo.getCompanyZip());
            ancillaryProductInfo.setCompanyName(providerInfo.getCompanyName());
            ancillaryProductInfo.setCompany(providerInfo.getCompany());
            ancillaryProductInfo.setProviderId(providerInfo.getProviderId());
            ancillaryProductInfo.setDealerCode(providerInfo.getDealerId());
            ancillaryProductInfo.setEmailTo(providerInfo.getEmail());
        });
        if (CollectionUtils.isNotEmpty(emptyProviderInfo)){
            try {
                File file = generateExcel(emptyProviderInfo, mailHeaders);
            CommonEmailInfo errorEmailInfo = CommonEmailInfo.builder()
                    .emailFrom(errorMailFrom)
                    .emailHost(emailHost)
                    .emailPort(emailPort)
                    .emailContent("Because there is no information about the providers, the email will not be sent. See the attachment for details.")
                    .emailCc(errorMailCc)
                    .emailTo(errorMailTo)
                    .subject("Technical Design For Ancillary Product Cancellation Notice - Sending error")
                    .file(file)
                    .fileName(file.getName())
                    .ltrDepartment("WARRANTY")
                    .build();
            sendMailService.send(errorEmailInfo);
            }catch (ExcelGenerationException e){
                log.error("Generate Excel Error: {}", e.getMessage());
            }
        }
        return backupList;
    }

    private void sendProviderEmail(List<AncillaryProductInfo> ancillaryProductInfoList) throws EmailException {
        AtomicReference<String> emailTo = new AtomicReference<>();
        ancillaryProductInfoList.forEach(ancillaryProductInfo -> {
            try {
                ancillaryProductInfo.setSentType("Provider");
                ancillaryProductInfo.setReferenceName(ancillaryProductInfo.getProviderName());
                if (Double.parseDouble(ancillaryProductInfo.getEstimatedRefundDue()) <= 0) {
                    // Do not send letter if no refund.
                    log.info("Provider first letter not sent due to no refund: accNbr=" + ancillaryProductInfo.getAccNbr() + ", product=" + ancillaryProductInfo.getProduct() + ", id=" + ancillaryProductInfo.getContractId());
                    Map<String,String> eventLog = new HashMap<>();
                    eventLog.put("accNbr",ancillaryProductInfo.getAccNbr());
                    eventLog.put("product",ancillaryProductInfo.getProduct());
                    eventLog.put("providerId",ancillaryProductInfo.getProviderId());
                    eventLog.put("message","Provider first letter not sent due to no refund");
                    eventLog.put("dealerCode",ancillaryProductInfo.getDealerCode());
                    if (!"Y".equals(testMode)) {
                        insertEventLog(eventLog);
                    }
                    return;
                }

                Map<String,String> params = JsonUtility.jsonToObject(JsonUtility.objectToJson(ancillaryProductInfo), new TypeReference<Map<String, Object>>() {
                });
                String emailSubject;
                if (Double.parseDouble(ancillaryProductInfo.getEstimatedRefundDue()) < 100){
                    emailSubject = "Westlake Cancellation Request | " + ancillaryProductInfo.getProviderName() + " " + ancillaryProductInfo.getProduct() + " | " + "Account#: " + ancillaryProductInfo.getAccNbr();
                } else {
                    emailSubject = "Westlake Cancellation Request | " + ancillaryProductInfo.getProviderName() + " " + ancillaryProductInfo.getProduct() + " Refunds: $" + ancillaryProductInfo.getEstimatedRefundDue() + " | " + "Account#: " + ancillaryProductInfo.getAccNbr();
                }
                if ("Y".equals(testMode)) {
                    emailTo.set(testEmail);
                } else {
                    emailTo.set(ancillaryProductInfo.getEmailTo());
                }
                String city = StringUtils.defaultString(ancillaryProductInfo.getCity());
                String state = StringUtils.defaultString(ancillaryProductInfo.getState());
                String zip = StringUtils.defaultString(ancillaryProductInfo.getZip());
                params.put("cusCityStateZip",
                        String.format("%s, %s %s",
                                city.trim(),
                                state.trim(),
                                zip.trim()
                        ).replaceAll("\\s+", " ")
                );
                params.put("emailTo", emailTo.get());
                params.put("subject", emailSubject);
                params.put("estimatedRefundDue",stringToUsd(ancillaryProductInfo.getEstimatedRefundDue()));
                File file = fillTemplate(params);
                if (file == null){
                    return;
                }
                UUID uuid = UUID.randomUUID();
                CommonEmailInfo providerEmailInfo = CommonEmailInfo.builder()
                        .emailFrom(emailFrom)
                        .emailHost(emailHost)
                        .emailPort(emailPort)
                        .accNbr(ancillaryProductInfo.getAccNbr())
                        .dealerCode(ancillaryProductInfo.getDealerCode())
                        .emailContent(CommonConstant.ANCILLARY_PROVIDER_CONTENT)
                        .emailCc(emailCc)
                        .emailTo(emailTo.get())
                        .subject(emailSubject)
                        .ltrDepartment("WARR_CLAIM")
                        .uuid(uuid.toString())
                        .file(file)
                        .fileName(file.getName())
                        .emailType("PROVIDER")
                        .product(ancillaryProductInfo.getProduct())
                        .cancelDt(ancillaryProductInfo.getEventDt())
                        .build();
                sendMailService.send(providerEmailInfo);

                if (!"Y".equals(testMode)) {
                    warDao.insertLetterData(providerEmailInfo);
                }
            } catch (IOException | EmailException e) {
                throw new RuntimeException(e);
            }
        });
    }

    private void sendFileNet(String accNbr, File file) throws IOException {
        LinkedHashMap<String, Object> filenetMap = buildFilenetData(accNbr, Base64.getEncoder().encodeToString(fileToBytes(file)), file.getName());
        uploadDocument(filenetMap);
    }

    private void insertProviderLetterHistory(Map<String,String> param){
        alloyWriteDao.insertAncillaryCancellationLettersHistory(param);
    }

    public byte[] fileToBytes(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
            return baos.toByteArray();
        }
    }

    private String selectTemplateName(Map<String, String> map) {
        boolean useInopTemplate = shouldUseInopTemplate(map);
        if (useInopTemplate){
            map.put("mileage","INOP*");
        }
        return getTemplateName(map.get("company"), useInopTemplate);
    }

    private String getTemplateName(String company, boolean useInopTemplate) {
        if (StringUtils.isBlank(company)){
            return "";
        }
        if (useInopTemplate) {
            switch (company) {
                case "C-WF": return INOP_WFS_TEMPLATE;
                case "C-WFI": return INOP_WFI_TEMPLATE;
                case "C-WPM": return INOP_WPM_TEMPLATE;
                default: return "";
            }
        } else {
            switch (company) {
                case "C-WF": return WFS_TEMPLATE;
                case "C-WFI": return WFI_TEMPLATE;
                case "C-WPM": return WPM_TEMPLATE;
                default: return "";
            }
        }
    }

    private boolean shouldUseInopTemplate(Map<String, String> map) {
        Integer autoCheckMileageCount = warDao.getAutoCheckMileageCount(map.get("accNbr"), map.get("product"));
        return autoCheckMileageCount != 0 || StringUtils.isEmpty(map.get("mileage")) || "0".equals(map.get("mileage"));
    }

    private void insertEventLog(Map<String, String> map) {
        EventLog eventLog = new EventLog();
        eventLog.setLogType("Ancillary Product Cancellation");
        eventLog.setBussName("WARR_CLAIM");
        eventLog.setCreatedBy("Simcoe-Warranty-Batch");
        eventLog.setAccNbr(map.get("accNbr"));
        eventLog.setProduct(map.get("product"));
        eventLog.setProviderId(map.get("providerId"));
        eventLog.setMessage(map.get("message"));
        eventLog.setDealerCode(map.get("dealerCode"));

        warDao.addEventLogInfo(eventLog);
    }


    public void uploadDocument(Map<String, Object> filenetMap) {
        FileUpload fileUpload = new FileUpload();
        String url = filenetInternalurl + "/uploadDocument";
        log.info("upload   Document URL  " + url);
        try {
            String loanDocName = (String) filenetMap.get("loanDocName");
            String accNbr = (String) filenetMap.get("accNbr");
            String fileBody = (String) filenetMap.get("fileBody");
            String fileName = (String) filenetMap.get("fileName");
            Map<String, String> map = new HashMap<>();
            map.put("Loan Doc Name", loanDocName);
            map.put("Loan Account Number", accNbr);

            byte[] fileData = StringUtils.isEmpty(fileBody) ? null
                    : Base64.getDecoder().decode(fileBody);

            fileUpload.setFileName(fileName);
            fileUpload.setDocClass("Loan Documents");
            fileUpload.setContentType("application/pdf");
            fileUpload.setFile(fileData);
            fileUpload.setCustomProperties(map);
            log.info("upload   Document started " + fileUpload.getFileName());
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, fileUpload, String.class);
            log.info("upload   Document  end " + fileUpload.getFileName());
            if (200 != responseEntity.getStatusCode().value()) {
                log.error("Consumed message [upload documents to filenet] failed ：" + responseEntity.getBody());
            }

        } catch (Exception e) {
            log.error("Consumed message [upload documents to filenet] failed ：" + e);
        }
    }

    public LinkedHashMap<String, Object> buildFilenetData(String accNbr, String fileBody, String fileName) {
        LinkedHashMap<String, Object> filenetMap = new LinkedHashMap<>();
            filenetMap.put("appName", "Simcoe-Warranty-Batch");
            filenetMap.put("bussType", "FILENET");
            filenetMap.put("bussSubType", "FIRST_LETTER_FILENET");
            filenetMap.put("accNbr", accNbr);
            filenetMap.put("loanDocName", "Warranty Recovery");
            filenetMap.put("docType", "LOAN_DOCUMENTS");
            filenetMap.put("contentType", "application/pdf");
            filenetMap.put("fileBody", fileBody);
            filenetMap.put("fileName", fileName);
            filenetMap.put("DocClass", "Loan Documents");
            return filenetMap;

    }

    public static String stringToUsd(String numericString) {
        try {
            BigDecimal amount = new BigDecimal(numericString.trim());
            NumberFormat formatter = NumberFormat.getCurrencyInstance(Locale.US);
            return formatter.format(amount);
        } catch (NumberFormatException e) {
            return "$0.00";
        }
    }
}

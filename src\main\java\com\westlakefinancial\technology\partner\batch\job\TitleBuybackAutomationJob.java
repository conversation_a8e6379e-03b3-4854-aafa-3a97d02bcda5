package com.westlakefinancial.technology.partner.batch.job;
import com.westlakefinancial.technology.partner.batch.domain.TitleBuyBackAutomationDTO;
import com.westlakefinancial.technology.partner.batch.listener.JobExitCodeListener;
import com.westlakefinancial.technology.partner.batch.reader.SendTitleBuybackAutomationReader;
import com.westlakefinancial.technology.partner.batch.writer.SendTitleBuybackAutomationWriter;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@Configuration
public class TitleBuybackAutomationJob {

    @Resource
    private JobBuilderFactory jobBuilderFactory;
    @Resource
    private StepBuilderFactory stepBuilderFactory;
    @Resource
    private SendTitleBuybackAutomationReader sendTitleBuybackAutomationReader;
    @Resource
    private SendTitleBuybackAutomationWriter sendTitleBuybackAutomationWriter;
    @Resource
    private JobExitCodeListener jobExitCodeListener;

    @Bean
    public Job sendTitleBuybackAutomationBatchJob() {
        return jobBuilderFactory.get("sendTitleBuybackAutomationJob")
                .incrementer(new TimestampIncrementer())
                .start(sendTitleBuybackAutomationStep())
                .listener(jobExitCodeListener)
                .build();
    }

    @Bean
    public Step sendTitleBuybackAutomationStep() {
        return stepBuilderFactory.get("sendCancelDealerLettersStep")
                .<TitleBuyBackAutomationDTO, TitleBuyBackAutomationDTO>chunk(1000)
                .reader(sendTitleBuybackAutomationReader)
                .writer(sendTitleBuybackAutomationWriter)
                .build();
    }
}

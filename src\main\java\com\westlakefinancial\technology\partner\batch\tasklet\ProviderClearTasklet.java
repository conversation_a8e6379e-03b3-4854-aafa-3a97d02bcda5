package com.westlakefinancial.technology.partner.batch.tasklet;

import com.westlakefinancial.technology.partner.batch.mapper.war.WarDao;
import lombok.extern.slf4j.Slf4j;
import com.westlakefinancial.technology.partner.batch.utils.Counter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

/**
 * @auther bf
 * @date 2025/4/5
 */
@Slf4j
@Component
public class ProviderClearTasklet implements Tasklet {

    @Resource
    private WarDao warDao;
    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        log.info("Starting ProviderClearTasklet execution.");

        Map<String, List<Map<String, Object>>> providerGroup = new HashMap<>();

        providerGroup.forEach((groupKey, wrongProviders) -> {
            log.info("Processing provider group: {}", groupKey);

            wrongProviders.forEach(wrongProvider -> {
                log.info("Updating contract provider ID for: {}", wrongProvider);
                try {
                    warDao.updateContractProviderId(wrongProvider);
                    log.info("Successfully updated contract provider ID.");
                } catch (Exception e) {
                    log.error("Failed to update contract provider ID for: {}", wrongProvider, e);
                }
            });

            log.info("Updating provider flag for group: {}", groupKey);
            warDao.updateProviderFlag(groupKey, "Y");
            log.info("Provider flag updated successfully for group: {}", groupKey);
        });

        log.info("Finished ProviderClearTasklet execution.");
        return RepeatStatus.FINISHED;
    }
}
